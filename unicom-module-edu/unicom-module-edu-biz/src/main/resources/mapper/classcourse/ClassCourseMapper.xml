<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getMyClassSchedule" resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.MyClassScheduleVO">
        SELECT
            ecc.date,
            ecc."begin_time",
            ecc."end_time",
            ecc."period",
            ec."courses_type" ,
            ec."name" AS course_name,
            ecl."class_name" ,
            eti."name" AS teacher_name,
            ecc.course_id,
            ecc.id class_course_id,
            ecc.is_check,
            ecc.department,
            ecc.teacher_id_string,
            ecc.is_leader_lecture,
            ecc.is_merge isMerge
        FROM
            "edu_class_course" ecc LEFT JOIN "edu_courses" ec ON ecc."course_id" = ec."id" AND ec."deleted" = '0'
                                   LEFT JOIN "edu_classroom_library" ecl ON ecc."classroom_id" = ecl ."id" AND ecl."deleted" = '0'
                                   LEFT JOIN "edu_teacher_information" eti ON ecc."teacher_id" = eti."id" AND eti ."deleted" = '0'
                                   LEFT JOIN "edu_plan" ep on ecc."plan_id" = ep."id"
        WHERE
            ecc."deleted" = '0'
          AND
            ecc ."course_id" IS NOT NULL
--           and
--             ep."status" = '1'
          and ecc.is_temporary = 0

          <if test="reqVO.isCover == 1">
              AND ecc ."course_id" != -1
          </if>
          AND
            ecc.class_id = #{reqVO.classID}
          <if test="reqVO.teacherId != null">
                AND ecc.teacher_id = #{reqVO.teacherId}
          </if>

        <if test="reqVO.startTime != null and reqVO.startTime != '' and reqVO.endTime != null and reqVO.endTime != ''" >
            AND  ecc."begin_time" >= #{reqVO.startTime}
            AND  ecc."end_time"  &lt;= #{reqVO.endTime}
        </if>

    </select>


    <select id="getOptionalCourse" resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.MyClassScheduleVO">
        SELECT
        ec."courses_type",
        ec."name" AS course_name,
        eer."class_start_time" AS begin_time,
        eer."class_end_time" AS end_time,
        ecl."class_name" ,
        eti."name" as teacher_name,
        eer."day_period" as period,
        eerc2."class_course_id" as classCourseId,
        ec.is_leader_lecture
        FROM
        "edu_courses" ec
        LEFT JOIN "edu_elective_release_courses" eerc ON ec.id = eerc.course_id AND eerc.deleted = '0'
        LEFT JOIN "edu_elective_trainee_selection" eets ON eets.release_course_id = eerc.id	AND eets.deleted = '0'
        LEFT JOIN edu_trainee et on eets.trainee_id = et.id and et.deleted = 0
        LEFT JOIN "edu_elective_release" eer ON eer ."id" = eets ."release_id" AND eer ."deleted" = '0'
        LEFT JOIN "edu_classroom_library" ecl ON eerc."classroom_id" = ecl ."id" AND ecl."deleted" = '0'
        LEFT JOIN "edu_teacher_information" eti ON eerc."teacher_id" = eti."id" AND eti ."deleted" = '0'
        LEFT JOIN "edu_elective_release_classes" eerc2
            ON eer."id" = eerc2."release_id" AND eerc2."deleted" = '0' and eerc2.class_id = et.class_id
        WHERE
          eets.trainee_id = #{reqVO.traineeId}
        AND
          ec.deleted = '0'

        <if test="reqVO.startTime != null and reqVO.startTime != '' and reqVO.endTime != null and reqVO.endTime != ''" >
            AND  eer."class_start_time" >= #{reqVO.startTime}
            AND  eer."class_end_time"  &lt;= #{reqVO.endTime}
        </if>

    </select>

    <select id="getEduTraineeId" resultType="java.lang.Long">
        select su.employee_id
        from system_users su
        where su.id = #{userId} and su.deleted = 0
    </select>

    <select id="getEduTraineeClassId" resultType="java.lang.Long">
        select et.class_id
        from edu_trainee et
                 join system_users su on et.id = su.employee_id
            and et.user_id = su.id and su.deleted = 0
        where et.user_id = #{userId} and et.deleted = 0
        order by et.create_time desc
        limit 1
    </select>


    <select id="getTimeTable"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassTimeTableInfoVO">
        select ecc.class_id,
               ecc.date,
               ecm.class_name,
               ec.name courseName,
               ec.courses_type,
               ec.educate_form_id,
               ec.activity_type,
               ecc.begin_time,
               ecc.end_time,
               ecc.period,
--                eti.name teacherName,
               ecl.class_name classroom,
               ecc.course_id,
               ecc.is_merge isMerge,
               ep.classroom_id,
               ecc.id,
               ecc.is_check,
               ecc.teacher_id_string,
               ecc.department,
               ecc.is_leader_lecture,
               ecc.is_change
        from edu_class_course ecc
                 left join edu_courses ec on ecc.course_id = ec.id
                 left join edu_class_management ecm on ecc.class_id = ecm.id
--                  left join edu_teacher_information eti on ecc.teacher_id = eti.id
                 left join edu_classroom_library ecl on ecc.classroom_id = ecl.id
                 left join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        where ecc.deleted = '0'
--                and ep.status = '1'
                and ecc.is_temporary = 0
               AND ecc.course_id is not null
        <if test="reqVO.classId != null">
            and ecc.class_id = #{reqVO.classId}
        </if>
        <if test="reqVO.tenantId != null">
            and ecc.tenant_id = #{reqVO.tenantId}
        </if>
        <if test="reqVO.teacherId != null">
          and ecc.department = false and #{reqVO.teacherId} = ANY(STRING_TO_ARRAY(ecc.teacher_id_string, ',')::int[])
        </if>
        <if test="reqVO.dateBeg != null and reqVO.dateBeg!= ''">
            and ecc.date &gt;= #{reqVO.dateBeg}
        </if>
        <if test="reqVO.dateEnd != null and reqVO.dateEnd!= ''">
            and ecc.date &lt;= #{reqVO.dateEnd}
        </if>
        <if test="reqVO.classIdList != null and reqVO.classIdList.size() > 0">
            and ecc.class_id in
            <foreach collection="reqVO.classIdList" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>

    </select>
    <select id="getCoursesListByClassIdAndCourseTypelist"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select
            ecc.id,
            ecc.class_id,
            ecc.course_id,
            ecc.begin_time,
            ecc.end_time,
            ecc.teacher_id,
            ecc.classroom_id,
            ecc.is_temporary,
            ecc.is_merge,
            ecc.is_change,
            ecc.create_time,
            ecc.plan_id,
            ecc."date",
            ecc.period,
            ecc.conflict_info
        from edu_class_course ecc
            left join edu_courses ec on ecc.course_id = ec.id and ec.deleted = 0
            left join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        where ecc.deleted = 0
--           and ep.status = '1'
          and ecc.is_temporary = 0
          <if test="classId != null">
              and ecc.class_id = #{classId}
          </if>
          <if test="courseTypeList != null and courseTypeList.size() > 0">
              and ec.courses_type in
              <foreach collection="courseTypeList" item="courseType" separator="," open="(" close=")">
                  #{courseType}
              </foreach>
          </if>
    </select>
    <select id="getCoursesListByClassTimeAndCourseTypelist"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select
            ecc.id,
            ecc.class_id,
            ecc.course_id,
            ecc.begin_time,
            ecc.end_time,
            ecc.teacher_id,
            ecc.classroom_id,
            ecc.is_temporary,
            ecc.is_merge,
            ecc.is_change,
            ecc.create_time,
            ecc.plan_id,
            ecc."date",
            ecc.period,
            ecc.conflict_info
        from edu_class_course ecc
            left join edu_courses ec on ec.id = ecc.course_id and ec.deleted = 0
            join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        where ecc.deleted = 0
--             and ep.status = '1'
            and ecc.is_temporary = 0
          <if test="courseTypeList != null and courseTypeList.size() > 0">
              and ec.courses_type in
              <foreach collection="courseTypeList" item="courseType" separator="," open="(" close=")">
                  #{courseType}
              </foreach>
          </if>
            and  (
                ecc.begin_time between #{startDateTime} and #{endDateTime}
                or
                ecc.end_time  between #{startDateTime} and #{endDateTime}
                or (ecc.begin_time &lt;= #{startDateTime} and ecc.end_time &gt;= #{endDateTime}))
    </select>
    <select id="getLectureInfoByTeacherId"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherLectureTimeRespVO"
            parameterType="java.lang.Long">
        select end_time,
               teacher_id,
               course_id
        from edu_class_course
        where deleted = 0
            and teacher_id = #{teacherId}
    </select>
    <select id="getSimpleList"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseSimpleRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseSimpleReqVO">
        select
            ecc.id classCourseId,
            ecc.course_id ,
            ec."name" courseName,
            ec.courses_type courseType,
            ecc."date" classDate,
            ecc.period dayPeriod,
            ecc.begin_time classStartTime,
            ecc.end_time classEndTime
        from edu_class_course ecc
            left join edu_courses ec on ec.id = ecc.course_id and ec.deleted = 0
            LEFT JOIN edu_plan ep on ecc."plan_id" = ep."id" and ep.deleted = 0
        where ecc.deleted = 0
            <if test="reqVO.classId != null ">
                and ecc.class_id = #{reqVO.classId}
            </if>
            <if test="reqVO.isCheck != null">
                and ecc.is_check = #{reqVO.isCheck}
            </if>
            and CAST(ecc."date" AS date) between #{reqVO.startDate} and #{reqVO.endDate}
            and ecc.is_temporary = 0
--             and ep."status" = '1'
        order by ecc.begin_time
    </select>
    <select id="selectRespVOById"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseRespVO"
            parameterType="java.lang.Long">
        select ecc.id,
               ecc.class_id,
               ecc.course_id,
               ecc.begin_time,
               ecc.end_time,
               ecc.teacher_id,
               ecc.classroom_id,
               ecc.is_temporary,
               ecc.is_merge,
               ecc.is_change,
               ecc.plan_id,
               ecc."date",
               ecc.period,
               ecc.is_check,
               ecc.conflict_info,
               CASE
                   WHEN ecc.department = false THEN t."teacher_names"
                   WHEN ecc.department = true THEN t.course_dept_name
               END AS teacherName,
               t."teacher_ids" as teacherIds,
               ecl."class_name"  as classroomName,
               ec."name"         AS courseName,
               ec."courses_type" as courseType,
               ec.educate_form_id,
               ec.activity_type,
               ec.theme_id
        FROM "edu_class_course" ecc
                 LEFT JOIN "edu_courses" ec ON ecc."course_id" = ec."id" AND ec."deleted" = '0'
                 LEFT JOIN "edu_classroom_library" ecl ON ecc."classroom_id" = ecl."id" AND ecl."deleted" = '0'
                 left join (select ecct.class_course_id   class_course_id,
                                   group_concat(eti.name) teacher_names,
                                   group_concat(ecct.teacher_id)   teacher_ids,
                                   group_concat(ecct.dept_name)  course_dept_name
                            from edu_class_course_teacher ecct
                                     left join edu_teacher_information eti
                                               on eti.id = ecct.teacher_id and eti.deleted = 0
                            where ecct.deleted = 0
                            group by ecct.class_course_id) t on t.class_course_id = ecc.id
        WHERE ecc."deleted" = '0'
          and ecc.id = #{id}
    </select>


    <select id="getClassTimeByPeriodAndDate"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select
            ecc.begin_time ,
            ecc.end_time
        from edu_class_course ecc
                 join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        where ecc.deleted = 0
--           and ep.status = '1'
          and ecc.is_temporary = 0
          and ecc.course_id = -1
          and ecc.period = #{dayPeriod}
          and ecc."date" = #{classDate}
    </select>
    <select id="getEndedCourse"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select ecc.*,
               ec.educate_form_id
        from edu_class_course ecc
        left join edu_courses ec on ecc.course_id =  ec.id
        left join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        where ecc.deleted = 0 and
              ecc.is_distributed = false and
              ecc.is_temporary = false and
              ecc.course_id > 0 and
              ecc.teacher_id_string is not null and
              ecc.department = false and
--               ep.status = '1' and
              ecc.end_time between #{endTime} and #{now}
    </select>
    <select id="getDepartmentCourse"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select ecc.*,
               ec.educate_form_id
        from edu_class_course ecc
                 left join edu_courses ec on ecc.course_id =  ec.id
                 left join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        where ecc.deleted = 0 and
            ecc.is_distributed = false and
            ecc.is_temporary = false and
            ecc.course_id > 0 and
            ecc.department = true and
--             ep.status = '1' and
            ecc.end_time between #{endTime} and #{now}
    </select>
    <select id="getCoursesListByClassIdAndCourseDate" resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        SELECT ecc.*
        FROM edu_class_course ecc
        LEFT JOIN edu_plan ep on ecc."plan_id" = ep."id" and ep.deleted = 0
        WHERE
          ecc.deleted = 0
          AND ecc.is_temporary = 0
          AND ep."status" = '1'
          AND ecc.class_id = #{classId}
          AND date = #{reportingTime}
          AND (period = '1' or period = '2')
    </select>
    <select id="selectTopicListJoinTeacher"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.dto.TeacherInformationClassCourseDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterReqVO">

        select
        ecc.id classCourseId,
        ecc.class_id classId,
        ecc.course_id courseId,
        ecc.begin_time beginTime,
        ecc.end_time endTime,
        case
        when ecc.department = false then etd.dept_id
        when ecc.department = true then ecct.teacher_id
        end as teacherDeptId,
        case
        when ecc.department = false then etd.teacher_id
        when ecc.department = true then ecct.teacher_id
        end as teacherId,
        ec.educate_form_id educateFormId,
        ecc.classroom_id classRoomId,
        ecc.is_merge isMerge,
        ecc."date",
        ecc.period,
        ecc.department
        from edu_class_course ecc
        join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
        join edu_class_course_teacher ecct on
        ecct.class_course_id = ecc.id and ecct.deleted = 0
        join edu_class_management ecm on ecm.id = ecc.class_id
        and ecm.deleted = 0
        join edu_courses ec on ec.id = ecc.course_id and ec.deleted = 0
        left join edu_teacher_dept etd on
        ecct.teacher_id = etd.teacher_id
        where ecc.deleted = 0
        and ecc.is_temporary = 0
        <if test="reqVO.courseEndTime != null">
            and ecc.end_time &lt;= #{reqVO.courseEndTime}
        </if>
          <if test="reqVO.classTerm != null">
              and ecm.semester = #{reqVO.classTerm}
          </if>

        <if test="reqVO.tenantId != null">
            and ecc.tenant_id = #{reqVO.tenantId}
        </if>

        <if test="reqVO.startTime != null">
            AND cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            AND cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
        </if>
    </select>
    <select id="selectOptionalListJoinTeacher"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.dto.TeacherInformationClassCourseDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterReqVO">
        select
            ecc.id classCourseId,
            ecc.class_id classId,
            eercs.course_id courseId,
            ecc.begin_time beginTime,
            ecc.end_time endTime,
            etd.dept_id teacherDeptId,
            eercs.teacher_id teacherId,
            ec.educate_form_id educateFormId,
            eercs.classroom_id classRoomId,
            ecc.is_merge isMerge,
            ecc."date",
            ecc.period,
            ecc.department
        from edu_class_course ecc
                 join edu_plan ep on ecc.plan_id = ep.id and ep.deleted = 0
--             and ep.status = '1'
                 join edu_elective_release_classes eerc on eerc.class_course_id = ecc.id
            and eerc.deleted = 0
                 join edu_elective_release_courses eercs on eerc.release_id = eercs.release_id
            and eercs.deleted = 0
                 join edu_class_management ecm on ecm.id = ecc.class_id
            and ecm.deleted = 0
                 join edu_courses ec on ec.id = eercs.course_id and ec.deleted = 0
                 left join edu_teacher_dept etd on
            eercs.teacher_id = etd.teacher_id
        where ecc.deleted = 0
        and ecc.is_temporary = 0
        and ecc.course_id = -1
        <if test="reqVO.courseEndTime != null">
            and ecc.end_time &lt;= #{reqVO.courseEndTime}
        </if>
        <if test="reqVO.classTerm != null">
            and ecm.semester = #{reqVO.classTerm}
        </if>

        <if test="reqVO.tenantId != null">
            and ecc.tenant_id = #{reqVO.tenantId}
        </if>

        <if test="reqVO.startTime != null">
            AND cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            AND cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
        </if>
    </select>
    <select id="getDistributedCourse"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select ecc.*,
               ec.educate_form_id
        from edu_class_course ecc
                 left join edu_courses ec on ecc.course_id =  ec.id
                 left join edu_plan ep on ecc.plan_id = ep.id
        where ecc.deleted = 0 and
            ecc.is_distributed = true and
            ecc.is_temporary = false and
            ecc.course_id > 0 and
            ecc.teacher_id_string is not null and
            ecc.department = false and
--             ep.status = '1' and
            ecc.end_time between #{endTime} and #{now}
    </select>
    <select id="getDistributedDepartmentCourse"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select ecc.*,
               ec.educate_form_id
        from edu_class_course ecc
                 left join edu_courses ec on ecc.course_id =  ec.id
                 left join edu_plan ep on ecc.plan_id = ep.id
        where ecc.deleted = 0 and
            ecc.is_distributed = true and
            ecc.is_temporary = false and
            ecc.course_id > 0 and
            ecc.department = true and
--             ep.status = '1' and
            ecc.end_time between #{endTime} and #{now}
    </select>
    <select id="selectClassCourseByClassIdsAndTime"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select
        id,
        class_id,
        course_id,
        begin_time,
        end_time,
        teacher_id,
        classroom_id,
        is_temporary,
        is_merge,
        is_change,
        creator,
        create_time,
        updater,
        update_time,
        deleted,
        plan_id,
        "date",
        period,
        is_check,
        conflict_info,
        is_distributed,
        tenant_id,
        original,
        department,
        teacher_id_string,
        dept_id,
        change_time,
        change_type,
        is_leader_lecture
        from edu_class_course
        where deleted = 0
        and class_id in
        <foreach collection="classIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and DATE_TRUNC('minute', begin_time ) = #{beginTime}
        and DATE_TRUNC('minute', end_time ) = #{endTime}
    </select>
    <select id="selectMergedClassCourse"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassCourseClassNameRespDTO"
            parameterType="java.lang.Long">
        select
            ecc.id,
            ecc.class_id,
            ecc.course_id,
            ecc.begin_time,
            ecc.end_time,
            ecc.teacher_id,
            ecc.classroom_id,
            ecc.is_temporary,
            ecc.is_merge,
            ecc.is_change,
            ecc.creator,
            ecc.create_time,
            ecc.updater,
            ecc.update_time,
            ecc.deleted,
            ecc.plan_id,
            ecc."date",
            ecc.period,
            ecc.is_check,
            ecc.conflict_info,
            ecc.is_distributed,
            ecc.tenant_id,
            ecc.original,
            ecc.department,
            ecc.teacher_id_string,
            ecc.dept_id,
            ecc.change_time,
            ecc.change_type,
            ecc.is_leader_lecture,
            ecm.class_name
        from edu_class_course ecc
                 left join edu_class_management  ecm on ecm.id = ecc.class_id
            and ecm.deleted = 0
        where ecc.deleted = 0
          and ecc.id != #{classCourseId}
          and ecc.id in (
            SELECT
                unnest(ARRAY_AGG(ecc.id))
            FROM
                edu_class_course ecc
            WHERE
                ecc.deleted = 0
              AND ecc.classroom_id IS NOT NULL
              AND ecc.is_temporary = 0
              AND ecc.course_id != -1
              AND ecc.is_merge = 1
            GROUP BY
                SUBSTRING(ecc.begin_time, 1, 17),
                SUBSTRING(ecc.end_time, 1, 17),
                ecc.classroom_id,
                ecc.course_id,
                ecc.teacher_id_string
            HAVING
                COUNT(ecc.id) > 1
               AND #{classCourseId} = ANY(ARRAY_AGG(ecc.id))
        )
    </select>
    <select id="getMergedClassListByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassCourseClassNameRespDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassMergeReqVO">
        select
            ecc.id,
            ecc.class_id,
            ecc.course_id,
            ecc.begin_time,
            ecc.end_time,
            ecc.teacher_id,
            ecc.classroom_id,
            ecc.is_temporary,
            ecc.is_merge,
            ecc.is_change,
            ecc.creator,
            ecc.create_time,
            ecc.updater,
            ecc.update_time,
            ecc.deleted,
            ecc.plan_id,
            ecc."date",
            ecc.period,
            ecc.is_check,
            ecc.conflict_info,
            ecc.is_distributed,
            ecc.tenant_id,
            ecc.original,
            ecc.department,
            ecc.teacher_id_string,
            ecc.dept_id,
            ecc.change_time,
            ecc.change_type,
            ecc.is_leader_lecture,
            ecm.class_name
        from
            edu_class_course ecc
                left join edu_class_management ecm on
                ecm.id = ecc.class_id
                    and ecm.deleted = 0
        where
            ecc.deleted = 0
<!--            and ecc.is_temporary = 0-->
            and ecc.course_id != -1
            and ecc.is_merge = 1
            <if test="reqVO.beginTime != null  and reqVO.endTime != null">
                and DATE_TRUNC('minute', ecc.begin_time ) = #{reqVO.beginTime}
                and DATE_TRUNC('minute', ecc.end_time ) = #{reqVO.endTime}
            </if>

            <if test="reqVO.period != null and reqVO.period!='' and reqVO.date != null and reqVO.date!=''">
                and ecc."date" = #{reqVO.date}
                and ecc.period = #{reqVO.period}
                and ecc.begin_time is null
                and ecc.end_time is null
            </if>

            and ecc.course_id = #{reqVO.courseId}
            <if test="reqVO.teacherIdString != null and reqVO.teacherIdString!=''">
                and ecc.teacher_id_string = #{reqVO.teacherIdString}
            </if>
    </select>



    <select id="getMergedClassListByCourseTimeBatch"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassCourseClassNameRespDTO"
            parameterType="java.util.List">
        select
        ecc.id,
        ecc.class_id,
        ecc.course_id,
        ecc.begin_time,
        ecc.end_time,
        ecc.teacher_id,
        ecc.classroom_id,
        ecc.is_temporary,
        ecc.is_merge,
        ecc.is_change,
        ecc.creator,
        ecc.create_time,
        ecc.updater,
        ecc.update_time,
        ecc.deleted,
        ecc.plan_id,
        ecc."date",
        ecc.period,
        ecc.is_check,
        ecc.conflict_info,
        ecc.is_distributed,
        ecc.tenant_id,
        ecc.original,
        ecc.department,
        ecc.teacher_id_string,
        ecc.dept_id,
        ecc.change_time,
        ecc.change_type,
        ecc.is_leader_lecture,
        ecm.class_name
        from
        edu_class_course ecc
        left join edu_class_management ecm on
        ecm.id = ecc.class_id
        and ecm.deleted = 0
        where
        ecc.deleted = 0
        <!--            and ecc.is_temporary = 0-->
        and ecc.course_id != -1
        and ecc.is_merge = 1

        AND (ecc.course_id, DATE_TRUNC('minute', ecc.begin_time), DATE_TRUNC('minute', ecc.end_time)) IN
        <foreach collection="reqList" item="item" separator="," open="(" close=")">
            (#{item.courseId}, #{item.beginTime}, #{item.endTime})
        </foreach>

    </select>

    <select id="selectDepartmentLeaderCourse"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO">
        select * from  edu_class_course where is_department_leader = true and deleted = 0 and is_temporary = false and begin_time > #{now}
    </select>
    <select id="getClassMergeCourseGroup"
            resultType="com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassMergeCourseGroupRespDTO">
        select
        dense_rank() over (
        order by
        case
        when ecc.begin_time is not null and ecc.end_time is not null
        then substring(ecc.begin_time, 1, 17)
        else ecc."date"
        end,
        case
        when ecc.begin_time is not null and ecc.end_time is not null
        then substring(ecc.end_time, 1, 17)
        else ecc.period
        end,
        ecc.teacher_id_string,
        ecc.course_id
        ) as groupNum,
        ecc.id,
        ecc.class_id,
        ecc.course_id,
        ecc.begin_time,
        ecc.end_time,
        ecc.teacher_id,
        ecc.classroom_id,
        ecc.is_temporary,
        ecc.is_merge,
        ecc.is_change,
        ecc.creator,
        ecc.create_time,
        ecc.updater,
        ecc.update_time,
        ecc.deleted,
        ecc.plan_id,
        ecc."date",
        ecc.period,
        ecc.is_check,
        ecc.conflict_info,
        ecc.is_distributed,
        ecc.tenant_id,
        ecc.original,
        ecc.department,
        ecc.teacher_id_string,
        ecc.dept_id,
        ecc.change_time,
        ecc.change_type,
        ecc.is_leader_lecture,
        ecm.class_name
        from
        edu_class_course ecc
        left join edu_class_management ecm
        on ecm.id = ecc.class_id and ecm.deleted = 0
        where
        ecc.deleted = 0
        and ecc.id in (
        select
        unnest(array_agg(ecc.id))
        from
        edu_class_course ecc
        where
        ecc.deleted = 0
        and ecc.course_id != -1
        and ecc.is_merge = 1
        group by
        case
        when ecc.begin_time is not null and ecc.end_time is not null
        then substring(ecc.begin_time, 1, 17)
        else ecc."date"
        end,
        case
        when ecc.begin_time is not null and ecc.end_time is not null
        then substring(ecc.end_time, 1, 17)
        else ecc.period
        end,
        ecc.course_id,
        ecc.teacher_id_string
        having
        count(ecc.id) > 1
        <if test="classCourseIds != null and classCourseIds.size() > 0">
            and exists (
            select 1
            from unnest(array_agg(ecc.id)) as id
            where id in
            <foreach collection="classCourseIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        )
    </select>


    <update id="updateClassCourseByClockingIn">
        update
            edu_class_course
        set
            "is_check" = #{clockingInVO.isCheck}
        where
            id = #{clockingInVO.id}
    </update>

    <select id="getTeachingRecordPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeachingRecordRespVO">
        <!-- 查询教师授课记录 -->
        WITH base_query AS (
            SELECT
                ecc.id AS class_course_id,
                ec.id AS course_id,
                ec.name AS course_name,
                ecm.id AS class_id,
                ecm.class_name AS class_name,
                eti.name AS teacher_name,
                ecc.begin_time AS begin_time,
                ecc.end_time AS end_time,
                COALESCE(tm.label, '其他') AS teachmethod,
                ec.teaching_method_id,
                ecc.tenant_id
            FROM
                edu_class_course ecc
                    LEFT JOIN
                edu_courses ec ON ecc.course_id = ec.id AND ec.deleted = 0
                    LEFT JOIN
                edu_class_management ecm ON ecc.class_id = ecm.id AND ecm.deleted = 0
                    LEFT JOIN
                edu_class_course_teacher ecct ON ecct.class_course_id = ecc.id AND ecct.deleted = 0
                    LEFT JOIN
                edu_teacher_information eti ON ecct.teacher_id = eti.id AND eti.deleted = 0
                    LEFT JOIN
                system_dict_data tm ON ec.teaching_method_id = tm.id
            WHERE
                ecc.deleted = 0
              AND ecc.is_temporary = 0
              AND ecct.teacher_id = #{reqVO.teacherId}
            <if test="reqVO.className != null and reqVO.className != ''">
                AND ecm.class_name LIKE CONCAT('%', #{reqVO.className}, '%')
            </if>
            <if test="reqVO.courseName != null and reqVO.courseName != ''">
                AND ec.name LIKE CONCAT('%', #{reqVO.courseName}, '%')
            </if>
            <if test="reqVO.beginTime != null">
                AND ecc.begin_time >= #{reqVO.beginTime}
            </if>
            <if test="reqVO.endTime != null">
                AND ecc.end_time &lt;= #{reqVO.endTime}
            </if>
            <if test="reqVO.teachingMethod != null">
                AND ec.teaching_method_id = #{reqVO.teachingMethod}
            </if>
        )
        SELECT
            ROW_NUMBER() OVER (ORDER BY t.begin_time DESC) AS serial_number,
            t.class_course_id,
            t.course_id,
            t.course_name,
            t.class_id,
            t.class_name,
            t.teacher_name,
            t.begin_time,
            t.end_time,
            t.teachmethod,
            t.teaching_method_id,
            t.total
        FROM (
                 SELECT
                     bq.*,
                     COUNT(1) OVER() AS total
                 FROM
                     base_query bq
             ) t
        ORDER BY
            t.begin_time DESC
        LIMIT #{reqVO.pageSize} OFFSET #{reqVO.pageSize} * (#{reqVO.pageNo} - 1)
    </select>
</mapper>
