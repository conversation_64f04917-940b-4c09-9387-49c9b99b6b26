package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 班级学员考勤详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassAttendanceDetailPageReqVO extends PageParam {

    @ApiModelProperty(value = "班级ID", required = true, example = "1024")
    private Long classId;

    @ApiModelProperty(value = "学员姓名，为空时查询所有学员", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "考勤状态：0-未到，1-正常，2-迟到，3-事假，4-病假，5-五会假，为空时查询所有状态。" +
            "注意：3/4/5都对应数据库trainee_status=3，通过leave_type区分：3对应leave_type=1，4对应leave_type=2，5对应leave_type=3", example = "1")
    private Integer status;

    @ApiModelProperty(value = "考勤日期，格式：yyyy-MM-dd，为空时查询所有日期", example = "2024-07-01")
    private String clockDate;

    @ApiModelProperty(value = "排课表ID，为空时查询所有排课", example = "1024")
    private Long classCourseId;

    @ApiModelProperty(value = "考勤类型：0-到课，1-就餐，2-住宿，为空时查询所有类型", example = "0")
    private Integer type;

    @ApiModelProperty(value = "就餐时段：0-早餐，1-午餐，2-晚餐，为空时查询所有时段", example = "0")
    private Integer mealPeriod;

    // 注释掉clockInType字段，因为与status字段功能重复，都对应数据库中的trainee_status字段
    // @ApiModelProperty(value = "打卡类型：0-未打卡，1-已打卡，2-迟到，3-请假，为空时查询所有打卡类型（与status参数功能相同，优先使用status）", example = "0")
    // private Integer clockInType;

    // 注释掉leaveType字段，改为通过status参数控制请假类型（status=3事假，status=4病假，status=5五会假）
    // @ApiModelProperty(value = "请假类型：1-事假，2-病假，3-五会假，为空时查询所有请假类型", example = "1")
    // private Integer leaveType;
}
