package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 班级学员考勤详情 Response VO")
@Data
@ToString(callSuper = true)
public class ClassAttendanceDetailRespVO {

    @ApiModelProperty(value = "ID", required = true, example = "1024")
    private Integer id;

    @ApiModelProperty(value = "序号", example = "1")
    private Integer serialNumber;

    @ApiModelProperty(value = "学员ID", required = true, example = "2048")
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名", required = true, example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "考勤时间", required = true)
    private LocalDateTime attendanceTime;

    @ApiModelProperty(value = "考勤状态", required = true, example = "1", notes = "0-未到 1-正常 2-迟到 3-事假 4-病假 5-五会假")
    private Integer status;

    @ApiModelProperty(value = "状态描述", example = "正常")
    private String statusDesc;

    @ApiModelProperty(value = "班级ID", required = true, example = "100")
    private Long classId;

    @ApiModelProperty(value = "班级名称", required = true, example = "2024春季培训班")
    private String className;

    @ApiModelProperty(value = "请假类型", example = "1", notes = "1-事假 2-病假 3-五会假")
    private Integer leaveType;

    @ApiModelProperty(value = "请假类型描述", example = "事假")
    private String leaveTypeDesc;
}
