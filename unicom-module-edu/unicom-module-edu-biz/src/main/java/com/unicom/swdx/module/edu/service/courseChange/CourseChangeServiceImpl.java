package com.unicom.swdx.module.edu.service.courseChange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassCourseClassNameRespDTO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassMergeCourseGroupRespDTO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseUpdateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassMergeReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.WeekTimetableReqVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO;
import com.unicom.swdx.module.edu.convert.classcourse.ClassCourseConvert;
import com.unicom.swdx.module.edu.convert.coursechange.CourseChangeConvert;
import com.unicom.swdx.module.edu.convert.teacherinformation.TeacherInformationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.coursechange.CourseChangeDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourseteacher.ClassCourseTeacherMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.classroomlibrary.ClassroomLibraryMapper;
import com.unicom.swdx.module.edu.dal.mysql.coursechange.CourseChangeMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.enums.classcourse.CourseChangeTypeEnum;
import com.unicom.swdx.module.edu.enums.classcourse.PeriodEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesPeriodEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.plan.PlanStatusEnum;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseServiceImpl;
import com.unicom.swdx.module.edu.service.classcourseteacher.ClassCourseTeacherService;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementServiceImpl;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.plan.PlanService;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationService;
import com.unicom.swdx.module.edu.utils.classcourse.ClassCourseUtil;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import io.prometheus.client.CollectorRegistry;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.shaded.freemarker.ext.beans._BeansAPI;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static com.unicom.swdx.framework.common.util.date.DateUtils.truncateToMinute;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.service.classcourse.ClassCourseServiceImpl.CONFLICT_INFO_TEMPLATE;
import static com.unicom.swdx.module.edu.service.classcourse.ClassCourseServiceImpl.isTimeOverlap;

/**
 * 调课 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CourseChangeServiceImpl extends ServiceImpl<CourseChangeMapper, CourseChangeDO>  implements CourseChangeService {

    @Resource
    private CourseChangeMapper courseChangeMapper;

    @Resource
    private ClassCourseServiceImpl classCourseService;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private PlanService planService;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private EvaluationResponseMapper evaluationResponseMapper;

    @Resource
    private ClockInInfoService clockInInfoService;

    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Resource
    private TeacherInformationService teacherInformationService;

    @Resource
    private ClassCourseTeacherMapper classCourseTeacherMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private ClassCourseUtil classCourseUtil;

    @Resource
    @Lazy
    private CourseChangeService courseChangeService;


    /**
     * 调剂记录分页
     *
     * @param reqVO 查询条件
     * @return 分页列表
     */
    @Override
    public PageResult<CourseChangeRespVO> getCourseChangePage(CourseChangePageReqVO reqVO) {
        IPage<CourseChangeRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<CourseChangeRespVO> list = courseChangeMapper.selectPageByReqVO(page, reqVO);
        list.forEach(item->{
            item.setClassInfoBeforeDetail(getClassInfoDetail(item.getClassInfoBefore()));
            item.setClassInfoAfterDetail(getClassInfoDetail(item.getClassInfoAfter()));
        });
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 调剂记录导出
     *
     * @param exportReqVO 查询条件
     * @return 导出列表
     */
    @Override
    public List<CourseChangeExcelVO> getCourseChangeExportList(CourseChangeExportReqVO exportReqVO) {
        List<CourseChangeDO> list = this.list(new LambdaQueryWrapperX<CourseChangeDO>()
                .eqIfPresent(CourseChangeDO::getChangeType, exportReqVO.getChangeType())
                .likeIfPresent(CourseChangeDO::getClassName, exportReqVO.getClassName())
                .orderByDesc(CourseChangeDO::getCreateTime));
        List<CourseChangeExcelVO> exportList = CourseChangeConvert.INSTANCE.convertExportList(list);
        for (CourseChangeExcelVO vo : exportList) {
            vo.setClassInfoBefore(getClassInfoExportDetail(vo.getClassInfoBefore()));
            vo.setClassInfoAfter(getClassInfoExportDetail(vo.getClassInfoAfter()));
        }
        return exportList;
    }

    private Map<String, Object> getClassInfoDetail(String classInfo){
        if(StrUtil.isNotBlank(classInfo)){
            String[] split = classInfo.split("@#");
            return new HashMap<String, Object>() {{
                put("courseName", split[0]);
                put("teacherName", split[1]);
                put("classroom", split[2]);
                put("dayTime", split[3]);
            }};
        }
        return null;
    }

    private String getClassInfoExportDetail(String classInfo){
        if(StrUtil.isNotBlank(classInfo)){
            String[] split = classInfo.split("@#");
            return "课程名称："+split[0]+"/"+"授课教师："+split[1]+"/"+"上课地点："+split[2]+"/"+"上课时间："+split[3];
        }
        return null;
    }

    //--------------------------------------------------------------------------------

    @Override
    public List<WeekTimetableRespVO> getTimetableOfWeek(WeekTimetableReqVO reqVO) {
//        校验查询的课表日期在教学计划日期范围内
//        PlanDO plan = planService.getPlanByClassId(reqVO.getClassId());
//        if(Objects.isNull(plan)){
//            throw exception(PLAN_NOT_EXIST);
//        }
//        LocalDate dateBeg = LocalDate.parse(reqVO.getDateBeg(), dateFormatter);
//        LocalDate dateEnd = LocalDate.parse(reqVO.getDateEnd(), dateFormatter);
//        LocalDate planBeg = LocalDate.parse(plan.getBeginDate(), dateFormatter);
//        LocalDate planEnd = LocalDate.parse(plan.getEndDate(), dateFormatter);
//        if(dateBeg.isAfter(planEnd) || dateEnd.isBefore(planBeg)){
//            return null;
//        }
//        reqVO.setPlanId(plan.getId());
        //查询该班级的排课表
        List<WeekTimetableInfoDTO> list = courseChangeMapper.getWeekTimetable(reqVO);

        //适配多教师
        // 老师
        List<Long> teacherIds = list.stream()
                .map(WeekTimetableInfoDTO::getTeacherIdString)
                .filter(Objects::nonNull)
                .flatMap(teacherIdString -> Arrays.stream(teacherIdString.split(",")))
                .map(String::trim)
                .filter(id -> {
                    try {
                        // 尝试转换为 Long
                        Long.parseLong(id);
                        return true;
                    } catch (NumberFormatException e) {
                        // 如果抛出异常，则过滤掉
                        return false;
                    }
                })
                .map(Long::valueOf)
                .distinct() // 去除重复的 ID
                .collect(Collectors.toList());
        List<TeacherInformationDO> teacherInformationList = !teacherIds.isEmpty() ? teacherInformationService.listByIds(teacherIds) : new ArrayList<>();
        Map<Long, TeacherInformationDO> teacherInformationMap = teacherInformationList.stream()
                .collect(Collectors.toMap(TeacherInformationDO::getId, cmDO -> cmDO));
//        Map<String, List<WeekTimetableInfoDTO>> groupedByDate = list.stream()
//                .collect(Collectors.groupingBy(
//                        WeekTimetableInfoDTO::getDate,
//                        Collectors.collectingAndThen(
//                                Collectors.toList(),
//                                courseList -> courseList.stream()
//                                        .sorted(Comparator.comparing(WeekTimetableInfoDTO::getBeginTime))
//                                        .collect(Collectors.toList()))));

        //防止空指针
        Map<String, List<WeekTimetableInfoDTO>> groupedByDate = Optional.of(list).orElse(Collections.emptyList()).stream()
                .filter(item -> item.getDate() != null)
                .collect(Collectors.groupingBy(
                        WeekTimetableInfoDTO::getDate,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                courseList -> courseList.stream()
                                        .sorted(Comparator.comparing(
                                                WeekTimetableInfoDTO::getBeginTime,
                                                Comparator.nullsLast(Comparator.naturalOrder())))
                                        .collect(Collectors.toList())
                        )
                ));
        // 获取指定排课id列表的合班排课分组情况 分组内的排课id数量必大于1
        List<ClassMergeCourseGroupRespDTO> classMergeCourseGroup = classCourseMapper.getClassMergeCourseGroup(list.stream().map(WeekTimetableInfoDTO::getId).collect(Collectors.toList()));
        // 所有有合班授课的排课id集合
        Set<Long> classMergeCourseIds = classMergeCourseGroup.stream()
                .map(ClassMergeCourseGroupRespDTO::getId)
                .collect(Collectors.toSet());
        List<WeekTimetableRespVO> respList = new ArrayList<>();
        groupedByDate.forEach((date, courses) -> {
            WeekTimetableRespVO respVO = new WeekTimetableRespVO();
            respVO.setDate(date);
            Map<String, List<WeekTimetableRespVO.CourseInfo>> map = new HashMap<>();
            List<WeekTimetableRespVO.CourseInfo> morning = new ArrayList<>();
            List<WeekTimetableRespVO.CourseInfo> afternoon = new ArrayList<>();
            List<WeekTimetableRespVO.CourseInfo> evening = new ArrayList<>();
            for (WeekTimetableInfoDTO course : courses) {
                WeekTimetableRespVO.CourseInfo courseInfo = new WeekTimetableRespVO.CourseInfo();
                courseInfo.setId(course.getId());
                courseInfo.setIsTemporary(course.getIsTemporary());
                // 是否显示合班授课标签
                courseInfo.setIsShowMergeTag(classMergeCourseIds.contains(course.getId()));
                //课程id为空，该单元格暂未排课
                if(Objects.nonNull(course.getCourseId())){
                    courseInfo.setCourseId(course.getCourseId());
                    courseInfo.setCourseType(course.getCourseType());
                    courseInfo.setTime(getTime(course.getBeginTime(), course.getEndTime()));
                    //课程id为-1，为选修课
                    if(Objects.equals(-1L,course.getCourseId())){
                        courseInfo.setCourseName("选修课");
                    }else {
                        courseInfo.setCourseName(course.getCourseName());
                        courseInfo.setTeacherId(course.getTeacherId());
                        courseInfo.setTeacher(course.getTeacherName());
                        // 补充老师信息
                        if(!course.getDepartment()){
                            if (StringUtils.isNotEmpty(course.getTeacherIdString())) {
                                // 以逗号分隔的教师ID字符串拆分并获取每个教师的信息
                                String[] teacherIdList = course.getTeacherIdString().split(",");
                                StringBuilder teacherNames = new StringBuilder();

                                for (String teacherIdStr : teacherIdList) {
                                    try {
                                        // 转换为Long类型
                                        Long teacherId = Long.valueOf(teacherIdStr.trim());
                                        TeacherInformationDO teacherInformationDO = teacherInformationMap.get(teacherId);
                                        if (teacherInformationDO != null) {
                                            if (teacherNames.length() > 0) {
                                                // 如果不是第一个教师，添加逗号
                                                teacherNames.append("，");
                                            }
                                            teacherNames.append(teacherInformationDO.getName());
                                        }
                                    } catch (NumberFormatException e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                // 设置教师名称，多个教师的名字用逗号分隔
                                course.setTeacherName(teacherNames.toString());
                            }
                        }else{
                            //部门授课
                            course.setTeacherName(course.getTeacherIdString());
                        }
                        courseInfo.setTeacher(course.getTeacherName());
                        courseInfo.setClassroomId(course.getClassroomId());
                        courseInfo.setClassroom(course.getClassroom());
                        courseInfo.setEducateFormId(course.getEducateFormId());
                        courseInfo.setIsMerge(course.getIsMerge());
                        courseInfo.setActivityType(course.getActivityType());
                    }
                }
                if (course.getPeriod().equals(CoursesPeriodEnum.MORNING.getType())){
                    morning.add(courseInfo);
                } else if (course.getPeriod().equals(CoursesPeriodEnum.AFTERNOON.getType())) {
                    afternoon.add(courseInfo);
                } else {
                    evening.add(courseInfo);
                }
            }
            map.put("上午", morning);
            map.put("下午", afternoon);
            map.put("晚上", evening);
            respVO.setCourses(map);
            respList.add(respVO);

        });
        return respList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exchange(CourseExchangeReqVO reqVO) {

        // TODO: 选修课发布后调课逻辑处理
        if(StrUtil.isBlank(reqVO.getChangeReason())){
            reqVO.setChangeReason("-");
        }

        // 查询并校验原申请课程和被调换课程
        ClassCourseInfoDTO applyCourse = courseChangeMapper.getClassInfo(reqVO.getApplyId());
        ClassCourseInfoDTO exchangeCourse = courseChangeMapper.getClassInfo(reqVO.getExchangeId());
        if(Objects.isNull(applyCourse) || Objects.isNull(exchangeCourse)){
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        if(applyCourse.getIsTemporary() || Objects.isNull(applyCourse.getCourseId())
            ||exchangeCourse.getIsTemporary() || (Objects.isNull(exchangeCourse.getCourseId()))){
            throw exception(CLASS_COURSE_CHANGE_NOT_EXISTS);
        }

        // 背景数据准备，用于后续判断是否需要对合班的课程进行对换
        // 判定合班授课的课程是否也需要对换，根据午别的课程数及个数顺序判断
        // 分别查询本班级申请课程和调换课程午别下的所有课程
        LambdaQueryWrapper<ClassCourseDO> applyWrapper1 = new LambdaQueryWrapper<ClassCourseDO>()
                .eq(ClassCourseDO::getClassId, applyCourse.getClassId())
                .eq(ClassCourseDO::getPeriod, applyCourse.getPeriod())
                .eq(ClassCourseDO::getDate, applyCourse.getDate());
        LambdaQueryWrapper<ClassCourseDO> exchangeWrapper1 = new LambdaQueryWrapper<ClassCourseDO>()
                .eq(ClassCourseDO::getClassId, exchangeCourse.getClassId())
                .eq(ClassCourseDO::getPeriod, exchangeCourse.getPeriod())
                .eq(ClassCourseDO::getDate, exchangeCourse.getDate());
        List<ClassCourseDO> applyClassCourseDOList = classCourseMapper.selectList(applyWrapper1);
        List<ClassCourseDO> exchangeClassCourseDOList = classCourseMapper.selectList(exchangeWrapper1);
        // 分别确定申请课程、交换课程的当天午别下所有课程的数量
        int applyClassCourseDOListSize = applyClassCourseDOList.size();
        // 内部排序，方便后续判断
        applyClassCourseDOList.sort(Comparator.comparing((ClassCourseDO o) -> o.getBeginTime() == null) // null 的排后面
                .thenComparing(ClassCourseDO::getBeginTime, Comparator.nullsLast(Comparator.naturalOrder())) // 有值时按时间升序
                .thenComparing(ClassCourseDO::getId));
        exchangeClassCourseDOList.sort(Comparator.comparing((ClassCourseDO o) -> o.getBeginTime() == null) // null 的排后面
                .thenComparing(ClassCourseDO::getBeginTime, Comparator.nullsLast(Comparator.naturalOrder())) // 有值时按时间升序
                .thenComparing(ClassCourseDO::getId));

        int exchangeClassCourseDOListSize = exchangeClassCourseDOList.size();
        // 根据申请课程、交换课程的id，去匹配在当天午别课程中的顺序，找出申请课程在当天午别的顺序位置
        OptionalInt applyOrderOptional = IntStream.range(0, applyClassCourseDOListSize)
                .filter(i -> applyClassCourseDOList.get(i).getId().equals(reqVO.getApplyId()))
                .findFirst();
        OptionalInt exchangeOrderOptional = IntStream.range(0, exchangeClassCourseDOListSize)
                .filter(i -> exchangeClassCourseDOList.get(i).getId().equals(reqVO.getExchangeId()))
                .findFirst();
        if (!applyOrderOptional.isPresent() || !exchangeOrderOptional.isPresent()) {
            throw new RuntimeException("未找到对应课程的午别顺序，请检查课程安排");
        }
        int applyOrder = applyOrderOptional.getAsInt();
        int exchangeOrder = exchangeOrderOptional.getAsInt();

        // 正式的调课流程，并记录调课记录
        exchangeClassCourses(reqVO, exchangeCourse, applyCourse);

        // 判定是否需要对合班课程进行对换
        if(applyCourse.getIsMerge()){
            ClassMergeReqVO classMergeReqVO = new ClassMergeReqVO();
            classMergeReqVO.setCourseId(applyCourse.getCourseId());
            // 若原课表格子开始、结束时间非空，则设置筛选条件的开始结束时间，否则使用日期和午别去定位
            if(applyCourse.getBeginTime() != null && applyCourse.getEndTime() != null){
                classMergeReqVO.setBeginTime(applyCourse.getBeginTime());
                classMergeReqVO.setEndTime(applyCourse.getEndTime());
            }else{
                classMergeReqVO.setDate(applyCourse.getDate());
                classMergeReqVO.setPeriod(applyCourse.getPeriod());
            }
            List<ClassCourseClassNameRespDTO> classInfoRespDTOS = classCourseMapper.getMergedClassListByReqVO(classMergeReqVO);
            // 排除自己
            classInfoRespDTOS = classInfoRespDTOS.stream().filter(classInfoRespVO -> !classInfoRespVO.getId().equals(applyCourse.getId())).collect(Collectors.toList());
            // 匹配到的合班信息非空
            if(CollectionUtil.isNotEmpty(classInfoRespDTOS)){
                // 收集无法兑换的对应合班课程的班级名称
                List<String> classManagementNames = new ArrayList<>();

//                // 1、创建线程池
//                ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
//                // 2、多线程计数器
//                CountDownLatch latch = new CountDownLatch(classInfoRespDTOS.size());
                for (ClassCourseClassNameRespDTO classInfoRespVO : classInfoRespDTOS) {
//                    executorService.submit(() -> {
//                        try {
                            exchangeMergeClassCourses(reqVO, classInfoRespVO, applyCourse, exchangeCourse, applyClassCourseDOList, exchangeClassCourseDOList, classManagementNames, applyClassCourseDOListSize, exchangeClassCourseDOListSize, applyOrder, exchangeOrder);
//                        }catch (Exception e){
//                            log.error(e.getMessage(), e);
//                            //重新抛出异常回滚
//                            throw e;
//                        }finally {
//                            latch.countDown();
//                        }
//                    });
                }
//                // 3. 主线程等待所有任务完成
//                try {
//                    latch.await();
//                } catch (InterruptedException e) {
//                    Thread.currentThread().interrupt();
//                }
//                // 4. 关闭线程池
//                executorService.shutdown();

                if(CollectionUtil.isNotEmpty(classManagementNames)){
                    String classNames = String.join("、", classManagementNames);
                    throw exception(CLASS_COURSE_MERGE_CHANGE_CONFLICT, classNames);
                }
            }
        }
        if(LocalDate.now().toString().equals(applyCourse.getDate())
                || LocalDate.now().toString().equals(exchangeCourse.getDate())){
            // 刷新签到表,立即提交任务，不等待结果
            CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                    .exceptionally(e -> {
                        log.error(e.getMessage(), e);
                        return null;
                    });
        }
    }

    /**
     * 交换合班课程对换方法
     * 该方法用于处理当申请调换的课程和被调换的课程涉及到合班授课时的对换逻辑
     * 它会检查是否需要对合班授课的课程进行对换，并执行对换操作
     *
     * @param reqVO 课程交换请求对象，包含申请交换课程和被交换课程的ID等信息
     * @param classInfoRespVO 班级课程信息响应对象，包含班级ID等信息
     * @param applyCourse 申请交换的课程信息
     * @param exchangeCourse 被交换的课程信息
     * @param applyClassCourseDOList 申请交换课程的班级课程列表
     * @param exchangeClassCourseDOList 被交换课程的班级课程列表
     * @param applyClassCourseDOListSize 申请交换课程列表的大小
     * @param exchangeClassCourseDOListSize 被交换课程列表的大小
     * @param applyOrder 申请交换课程在列表中的顺序
     * @param exchangeOrder 被交换课程在列表中的顺序
     */
    private void exchangeMergeClassCourses(CourseExchangeReqVO reqVO, ClassCourseClassNameRespDTO classInfoRespVO, ClassCourseInfoDTO applyCourse, ClassCourseInfoDTO exchangeCourse, List<ClassCourseDO> applyClassCourseDOList, List<ClassCourseDO> exchangeClassCourseDOList, List<String> classManagementNames, int applyClassCourseDOListSize, int exchangeClassCourseDOListSize, int applyOrder, int exchangeOrder) {
        // 背景数据准备，用于后续判断是否需要对合班的课程进行对换
        // 判定合班授课的课程是否也需要对换，根据午别的课程数及个数顺序判断
        // 分别查询本班级申请课程和调换课程午别下的所有课程
        LambdaQueryWrapper<ClassCourseDO> applyWrapper2 = new LambdaQueryWrapper<ClassCourseDO>()
                .eq(ClassCourseDO::getClassId, classInfoRespVO.getClassId())
                .eq(ClassCourseDO::getPeriod, applyCourse.getPeriod())
                .eq(ClassCourseDO::getDate, applyCourse.getDate());
        LambdaQueryWrapper<ClassCourseDO> exchangeWrapper2 = new LambdaQueryWrapper<ClassCourseDO>()
                .eq(ClassCourseDO::getClassId, classInfoRespVO.getClassId())
                .eq(ClassCourseDO::getPeriod, exchangeCourse.getPeriod())
                .eq(ClassCourseDO::getDate, exchangeCourse.getDate());
        List<ClassCourseDO> applyMergeClassCourseDOList = classCourseMapper.selectList(applyWrapper2);
        List<ClassCourseDO> exchangeMergeClassCourseDOList = classCourseMapper.selectList(exchangeWrapper2);
        // 内部排序，方便后续判断
        applyMergeClassCourseDOList.sort(Comparator.comparing((ClassCourseDO o) -> o.getBeginTime() == null) // null 的排后面
                .thenComparing(ClassCourseDO::getBeginTime, Comparator.nullsLast(Comparator.naturalOrder())) // 有值时按时间升序
                .thenComparing(ClassCourseDO::getId));
        exchangeMergeClassCourseDOList.sort(Comparator.comparing((ClassCourseDO o) -> o.getBeginTime() == null) // null 的排后面
                .thenComparing(ClassCourseDO::getBeginTime, Comparator.nullsLast(Comparator.naturalOrder())) // 有值时按时间升序
                .thenComparing(ClassCourseDO::getId));

        if(CollectionUtil.isEmpty(applyMergeClassCourseDOList) || CollectionUtil.isEmpty(exchangeMergeClassCourseDOList)){
            classManagementNames.add(classInfoRespVO.getClassName());
            return;
        }
        // 分别确定申请课程、交换课程的当天午别下所有课程的数量
        int applyMergeClassCourseDOListSize = applyMergeClassCourseDOList.size();
        int exchangeMergeClassCourseDOSize = exchangeMergeClassCourseDOList.size();

        // 根据申请课程、交换课程的id，去匹配在当天午别课程中的顺序，找出申请课程在当天午别的顺序位置
        OptionalInt applyOrderOptionalMerge = IntStream.range(0, applyMergeClassCourseDOListSize)
                .filter(i -> applyMergeClassCourseDOList.get(i).getId().equals(classInfoRespVO.getId()))
                .findFirst();
        if (!applyOrderOptionalMerge.isPresent()) {
            throw new RuntimeException("未找到对应课程的午别顺序，请检查课程安排");
        }
        int applyMergeOrder = applyOrderOptionalMerge.getAsInt();
        // 若合班课程的日期午别下的课程数和顺序与申请课程一致，合班班级的课程数与被调换课程的日期午别下的课程数一致，则进行课程对换
        if(applyMergeClassCourseDOListSize == applyClassCourseDOListSize && exchangeMergeClassCourseDOSize == exchangeClassCourseDOListSize && applyMergeOrder == applyOrder){
            CourseExchangeReqVO reqMergeVO = new CourseExchangeReqVO();
            reqMergeVO.setApplyId(classInfoRespVO.getId());
            ClassCourseDO targetCourse = exchangeMergeClassCourseDOList.get(exchangeOrder);
            reqMergeVO.setExchangeId(targetCourse.getId());
            reqMergeVO.setClassId(classInfoRespVO.getClassId());
            reqMergeVO.setClassName(classInfoRespVO.getClassName());
            // 对换相关的课程
            // 查询并校验原申请课程和被调换课程
            ClassCourseInfoDTO applyMergeCourse = courseChangeMapper.getClassInfo(reqMergeVO.getApplyId());
            ClassCourseInfoDTO exchangeMergeCourse = courseChangeMapper.getClassInfo(reqMergeVO.getExchangeId());
            if(Objects.isNull(applyMergeCourse) || Objects.isNull(exchangeMergeCourse)){
                throw exception(CLASS_COURSE_NOT_EXISTS);
            }
            if(applyMergeCourse.getIsTemporary() || Objects.isNull(applyMergeCourse.getCourseId())
                    ||exchangeMergeCourse.getIsTemporary() || (Objects.isNull(exchangeMergeCourse.getCourseId()))){
                throw exception(CLASS_COURSE_CHANGE_NOT_EXISTS);
            }
            exchangeClassCourses(reqMergeVO, exchangeMergeCourse, applyMergeCourse);
        }else{
            classManagementNames.add(classInfoRespVO.getClassName());
        }
    }

    /**
     * 交换课程信息
     * 当需要调换两节课时，使用此方法更新课程信息和相关记录
     *
     * @param reqVO 包含申请调课信息的请求对象
     * @param exchangeCourse 被申请调换的课程信息
     * @param applyCourse 申请调换的课程信息
     */
    private void exchangeClassCourses(CourseExchangeReqVO reqVO, ClassCourseInfoDTO exchangeCourse, ClassCourseInfoDTO applyCourse) {
        // 调课时间
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<ClassCourseDO> applyWrapper = new LambdaUpdateWrapper<ClassCourseDO>()
                .set(ClassCourseDO::getIsTemporary, false)
                .set(ClassCourseDO::getIsChange, true)
                .set(ClassCourseDO::getCourseId, exchangeCourse.getCourseId())
                .set(ClassCourseDO::getTeacherId, exchangeCourse.getTeacherId())
                .set(ClassCourseDO::getTeacherIdString, exchangeCourse.getTeacherIdString())
                .set(ClassCourseDO::getClassroomId, exchangeCourse.getClassroomId())
                // 调课时间
                .set(ClassCourseDO::getChangeTime, now)
                // 调课类型
                .set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.EXCHANGE.getCode())
                .eq(ClassCourseDO::getId, reqVO.getApplyId());
        LambdaUpdateWrapper<ClassCourseDO> exchangeWrapper = new LambdaUpdateWrapper<ClassCourseDO>()
                .set(ClassCourseDO::getIsTemporary, false)
                .set(ClassCourseDO::getIsChange, true)
                .set(ClassCourseDO::getCourseId, applyCourse.getCourseId())
                .set(ClassCourseDO::getTeacherId, applyCourse.getTeacherId())
                .set(ClassCourseDO::getTeacherIdString, applyCourse.getTeacherIdString())
                .set(ClassCourseDO::getClassroomId, applyCourse.getClassroomId())
                // 调课时间
                .set(ClassCourseDO::getChangeTime, now)
                // 调课类型
                .set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.EXCHANGE.getCode())
                .eq(ClassCourseDO::getId, reqVO.getExchangeId());
        //修改课程表
        classCourseService.update(applyWrapper);
        classCourseService.update(exchangeWrapper);
        //修改授课关系表，互换课表id
        List<Long> classCourseIds = new ArrayList<>();
        classCourseIds.add(reqVO.getApplyId());
        classCourseIds.add(reqVO.getExchangeId());
        LambdaQueryWrapper<ClassCourseTeacherDO> classCourseTeacherDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        classCourseTeacherDOLambdaQueryWrapper.in(ClassCourseTeacherDO::getClassCourseId, classCourseIds);
        List<ClassCourseTeacherDO> classCourseTeacherDOList = classCourseTeacherMapper.selectList(classCourseTeacherDOLambdaQueryWrapper);
        // 遍历查询结果，进行更新
        for (ClassCourseTeacherDO classCourseTeacherDO : classCourseTeacherDOList) {
            if (classCourseTeacherDO.getClassCourseId().equals(reqVO.getApplyId())) {
                classCourseTeacherDO.setClassCourseId(reqVO.getExchangeId());
            } else if (classCourseTeacherDO.getClassCourseId().equals(reqVO.getExchangeId())) {
                classCourseTeacherDO.setClassCourseId(reqVO.getApplyId());
            }
        }
        // 更新到数据库
        if(CollectionUtil.isNotEmpty(classCourseTeacherDOList)){
            classCourseTeacherMapper.updateBatch(classCourseTeacherDOList);
        }
        //课程互换，针对课程A与课程B生成两条调课记录
        List<CourseChangeDO> recordList = new ArrayList<>();
        CourseChangeDO changeRecord1 = getChangeRecordInfo(reqVO, applyCourse, exchangeCourse);
        CourseChangeDO changeRecord2 = getChangeRecordInfo(reqVO, exchangeCourse, applyCourse);
        changeRecord1.setChangeType(CourseChangeTypeEnum.EXCHANGE.getCode());
        changeRecord2.setChangeType(CourseChangeTypeEnum.EXCHANGE.getCode());
        recordList.add(changeRecord1);
        recordList.add(changeRecord2);
        courseChangeMapper.insertBatch(recordList);
//        if(LocalDate.now().toString().equals(applyCourse.getDate())
//                || LocalDate.now().toString().equals(exchangeCourse.getDate())){
//            // 刷新签到表,立即提交任务，不等待结果
//            CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
//                    .exceptionally(e -> {
//                        log.error(e.getMessage(), e);
//                        return null;
//                    });
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void change(CourseChangeReqVO reqVO) {
        // TODO: 选修课发布后调课逻辑处理
        if(StrUtil.isBlank(reqVO.getChangeReason())){
            reqVO.setChangeReason("-");
        }
        ClassCourseInfoDTO before = courseChangeMapper.getClassInfo(reqVO.getApplyId());
        if(Objects.isNull(before)){
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        //适配多教师
        // 老师
        List<Long> teacherIds = new ArrayList<>();
        if(StringUtils.isNotEmpty(before.getTeacherIdString())){
            teacherIds = Arrays.stream(before.getTeacherIdString() .split(",")).filter(id -> {
                        try {
                            // 尝试转换为 Long
                            Long.parseLong(id);
                            return true;
                        } catch (NumberFormatException e) {
                            // 如果抛出异常，则过滤掉
                            return false;
                        }
                    })
                    .map(Long::valueOf)
                    .distinct() // 去除重复的 ID
                    .collect(Collectors.toList());
        }
        if(StringUtils.isNotEmpty(reqVO.getTeacherIdString())){
            teacherIds.addAll(Arrays.stream(reqVO.getTeacherIdString() .split(",")).filter(id -> {
                        try {
                            // 尝试转换为 Long
                            Long.parseLong(id);
                            return true;
                        } catch (NumberFormatException e) {
                            // 如果抛出异常，则过滤掉
                            return false;
                        }
                    })
                    .map(Long::valueOf)
                    .distinct() // 去除重复的 ID
                    .collect(Collectors.toList()));
        }
        List<TeacherInformationDO> teacherInformationList = !teacherIds.isEmpty() ? teacherInformationService.listByIds(teacherIds) : new ArrayList<>();
        Map<Long, TeacherInformationDO> teacherInformationMap = teacherInformationList.stream()
                .collect(Collectors.toMap(TeacherInformationDO::getId, cmDO -> cmDO));
        // 补充老师信息
        if(!before.getDepartment()){
            if (StringUtils.isNotEmpty(before.getTeacherIdString())) {
                // 以逗号分隔的教师ID字符串拆分并获取每个教师的信息
                String[] teacherIdList = before.getTeacherIdString().split(",");
                StringBuilder teacherNames = new StringBuilder();

                for (String teacherIdStr : teacherIdList) {
                    try {
                        // 转换为Long类型
                        Long teacherId = Long.valueOf(teacherIdStr.trim());
                        TeacherInformationDO teacherInformationDO = teacherInformationMap.get(teacherId);
                        if (teacherInformationDO != null) {
                            if (teacherNames.length() > 0) {
                                // 如果不是第一个教师，添加逗号
                                teacherNames.append("，");
                            }
                            teacherNames.append(teacherInformationDO.getName());
                        }
                    } catch (NumberFormatException e) {
                        log.error(e.getMessage());
                    }
                }
                // 设置教师名称，多个教师的名字用逗号分隔
                before.setTeacherName(teacherNames.toString());
            }
        }else{
            //部门授课
            before.setTeacherName(before.getTeacherIdString());
        }
        //取消授课
        if(Objects.equals(reqVO.getIsCancel(), 1)){
            LambdaUpdateWrapper<ClassCourseDO> classCourseDOLambdaUpdateWrapper = emptyClassCourse(reqVO.getApplyId());
            // 调课时间
            classCourseDOLambdaUpdateWrapper.set(ClassCourseDO::getChangeTime, LocalDateTime.now());
            // 调课类型 取消授课
            classCourseDOLambdaUpdateWrapper.set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.CANCEL.getCode());
            //修改课程表-改成一个空白单元格
            classCourseService.update(classCourseDOLambdaUpdateWrapper);
            //添加调课记录
            ClassCourseInfoDTO after = new ClassCourseInfoDTO();
            after.setCourseId(null);
            CourseChangeDO changeRecord = getChangeRecordInfo(reqVO, before, after);
            changeRecord.setChangeType(CourseChangeTypeEnum.CANCEL.getCode());
            this.save(changeRecord);
            // 取消授课同时删除对应的评估问卷
            evaluationResponseMapper.delete(new LambdaQueryWrapperX<EvaluationResponseDO>().eq(EvaluationResponseDO::getClassCourseId, reqVO.getApplyId()));
            evaluationDetailMapper.delete(new LambdaQueryWrapperX<EvaluationDetailDO>().eq(EvaluationDetailDO::getClassCourseId, reqVO.getApplyId()));
            //取消授课删除相关联的考勤等信息
            List<Long> ccIds = new ArrayList<>();
            ccIds.add(reqVO.getApplyId());
            classCourseUtil.deleteClassCourseLinkInfo(ccIds);
            return;
        }
        int courseType;
        if(Objects.equals(-1L,before.getCourseId()) || Objects.isNull(before.getCourseType())){
            courseType = CoursesTypeEnum.OPTIONAL_COURSE.getType();
        }else {
            courseType = Integer.parseInt(before.getCourseType());
        }
        if(Objects.equals(CoursesTypeEnum.OPTIONAL_COURSE.getType(), courseType)){
            if(Objects.nonNull(reqVO.getClassroomId()) || Objects.nonNull(reqVO.getTeacherId())){
                throw exception(OPTIONAL_COURSE_NOT_SUPPORT_CHANGE_TEACHER_CLASSROOM);
            }
        }
        if(Objects.equals(CoursesTypeEnum.TEACHING_ACTIVITY.getType(), courseType)){
            if(Objects.nonNull(reqVO.getTeacherId())){
                throw exception(TEACHING_ACTIVITY_COURSE_NOT_SUPPORT_CHANGE_TEACHER);
            }
        }
        //更改教室/时间/老师
        boolean changeClassroom = false;
        boolean changeTeacher = false;
        boolean changeTime = false;
        ClassCourseInfoDTO after = new ClassCourseInfoDTO();
        BeanUtil.copyProperties(before, after);
        if(Objects.nonNull(reqVO.getClassroomId())){
            if(!Objects.equals(before.getClassroomId(), reqVO.getClassroomId())){
                after.setClassroomId(reqVO.getClassroomId());
                after.setClassroom(reqVO.getClassroom());
                changeClassroom = true;
            }
        }
        if(Objects.nonNull(reqVO.getTeacherId()) || Objects.nonNull(reqVO.getTeacherIdString())){
            if(!Objects.equals(before.getTeacherIdString(), reqVO.getTeacherIdString())){
                after.setTeacherId(reqVO.getTeacherId());
                after.setTeacherIdString(reqVO.getTeacherIdString());
                after.setTeacherName(reqVO.getTeacherName());
                // 补充老师信息
                if(!after.getDepartment()){
                    if (StringUtils.isNotEmpty(after.getTeacherIdString())) {
                        // 以逗号分隔的教师ID字符串拆分并获取每个教师的信息
                        String[] teacherIdList = after.getTeacherIdString().split(",");
                        StringBuilder teacherNames = new StringBuilder();
                        for (String teacherIdStr : teacherIdList) {
                            try {
                                // 转换为Long类型
                                Long teacherId = Long.valueOf(teacherIdStr.trim());
                                TeacherInformationDO teacherInformationDO = teacherInformationMap.get(teacherId);
                                if (teacherInformationDO != null) {
                                    if (teacherNames.length() > 0) {
                                        // 如果不是第一个教师，添加逗号
                                        teacherNames.append("，");
                                    }
                                    teacherNames.append(teacherInformationDO.getName());
                                }
                            } catch (NumberFormatException e) {
                                log.error(e.getMessage());
                            }
                        }
                        // 设置教师名称，多个教师的名字用逗号分隔
                        after.setTeacherName(teacherNames.toString());
                    }
                }else{
                    //部门授课
                    after.setTeacherName(after.getTeacherIdString());
                }
                changeTeacher = true;
            }
        }
        if(Objects.nonNull(reqVO.getChangeId())){
            after.setId(reqVO.getChangeId());
            ClassCourseInfoDTO change = courseChangeMapper.getClassInfo(reqVO.getChangeId());
            after.setDate(change.getDate());
            after.setPeriod(change.getPeriod());
            after.setBeginTime(change.getBeginTime());
            after.setEndTime(change.getEndTime());
            changeTime = true;
        }
        if(!(changeTime || changeTeacher || changeClassroom)){
            throw exception(CLASS_COURSE_CHANGE_PARAM_EMPTY);
        }

        // 合班授课同步调课
        syncMergeClassChange(reqVO, before, changeTime, after);

        LambdaUpdateWrapper<ClassCourseDO> wrapper = new LambdaUpdateWrapper<ClassCourseDO>()
                .set(ClassCourseDO::getIsTemporary, false)
                .set(ClassCourseDO::getIsChange, true)
                .set(ClassCourseDO::getChangeTime, LocalDateTime.now());
        CourseChangeDO changeRecord = getChangeRecordInfo(reqVO, before, after);

        // 收集所有触发的变更类型
        List<CourseChangeTypeEnum> triggeredChanges = new ArrayList<>();

        List<CourseChangeDO> recordList = new ArrayList<>();
        if(changeTime){
            //若曾经合班授课，则仍然保持合班授课
            if(before.getIsMerge()){
                wrapper.set(ClassCourseDO::getIsMerge, true);
            }
            wrapper.set(ClassCourseDO::getCourseId, before.getCourseId());
            wrapper.set(!changeClassroom, ClassCourseDO::getClassroomId, before.getClassroomId());
            wrapper.set(!changeTeacher, ClassCourseDO::getTeacherId, before.getTeacherId());

            //todo 在调课时仅调整时间，未调整教师的情况下，需要也调整老师信息，需要修改teacher_id_string字段，也需要修改教师授课关系表

            // 调课类型 更换上课时间
//            wrapper.set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.CHANGE_TIME.getCode());
            triggeredChanges.add(CourseChangeTypeEnum.CHANGE_TIME);
            wrapper.eq(ClassCourseDO::getId, reqVO.getChangeId());
            wrapper.set(ClassCourseDO::getTeacherIdString, before.getTeacherIdString());
            CourseChangeDO courseChangeDO = new CourseChangeDO();
            BeanUtil.copyProperties(changeRecord, courseChangeDO);
            courseChangeDO.setChangeType(CourseChangeTypeEnum.CHANGE_TIME.getCode());
            recordList.add(courseChangeDO);
            //将之前的单元格改成一个空白单元格
            LambdaUpdateWrapper<ClassCourseDO> classCourseDOLambdaUpdateWrapper = emptyClassCourse(reqVO.getApplyId());
//            // 调课时间
//            classCourseDOLambdaUpdateWrapper.set(ClassCourseDO::getChangeTime, LocalDateTime.now());
            // 调课类型 更换上课时间
            classCourseDOLambdaUpdateWrapper.set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.CHANGE_TIME.getCode());
            classCourseService.update(classCourseDOLambdaUpdateWrapper);

            //如果不换教师则执行，更新中间表数据
            if (!changeTeacher){
                List<Long> insertIds = new ArrayList<>();
                insertIds.add(reqVO.getApplyId());
                LambdaQueryWrapper<ClassCourseTeacherDO> classCourseTeacherDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                classCourseTeacherDOLambdaQueryWrapper.in(ClassCourseTeacherDO::getClassCourseId, insertIds);
                List<ClassCourseTeacherDO> classCourseTeacherDOList = classCourseTeacherMapper.selectList(classCourseTeacherDOLambdaQueryWrapper);

                if (CollectionUtil.isNotEmpty(classCourseTeacherDOList)) {
                    classCourseTeacherDOList.forEach(item -> item.setClassCourseId(reqVO.getChangeId()));
                }

                if(!classCourseTeacherDOList.isEmpty()){
                    classCourseTeacherMapper.updateBatch(classCourseTeacherDOList);
                }
            }

            //更换课程格子后删除相关联的考勤等信息
            List<Long> ccIds = new ArrayList<>();
            ccIds.add(reqVO.getApplyId());
            classCourseUtil.deleteClassCourseLinkInfo(ccIds);
        }else {
            wrapper.eq(ClassCourseDO::getId, reqVO.getApplyId());
        }
        if(changeClassroom){
            wrapper.set(ClassCourseDO::getClassroomId, reqVO.getClassroomId());
            CourseChangeDO courseChangeDO = new CourseChangeDO();
            BeanUtil.copyProperties(changeRecord, courseChangeDO);
            courseChangeDO.setChangeType(CourseChangeTypeEnum.CHANGE_CLASSROOM.getCode());
//            // 调课时间
//            wrapper.set(ClassCourseDO::getChangeTime, LocalDateTime.now());
            // 调课类型 更换教室
//            wrapper.set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.CHANGE_CLASSROOM.getCode());
            triggeredChanges.add(CourseChangeTypeEnum.CHANGE_CLASSROOM);
            recordList.add(courseChangeDO);
        }
        if(changeTeacher){
            wrapper.set(ClassCourseDO::getTeacherId, reqVO.getTeacherId());
            wrapper.set(ClassCourseDO::getTeacherIdString, reqVO.getTeacherIdString());
            wrapper.set(ClassCourseDO::getDepartment, false);
//            // 调课时间
//            wrapper.set(ClassCourseDO::getChangeTime, LocalDateTime.now());
            // 调课类型 更换教师
//            wrapper.set(ClassCourseDO::getChangeType, CourseChangeTypeEnum.CHANGE_TEACHER.getCode());
            triggeredChanges.add(CourseChangeTypeEnum.CHANGE_TEACHER);
            CourseChangeDO courseChangeDO = new CourseChangeDO();
            BeanUtil.copyProperties(changeRecord, courseChangeDO);
            courseChangeDO.setChangeType(CourseChangeTypeEnum.CHANGE_TEACHER.getCode());
            recordList.add(courseChangeDO);
            //删除原本自带的教师关联信息后,新增现有的关联信息
            List<Long> insertIds = new ArrayList<>();
            insertIds.add(reqVO.getApplyId());
            if(CollectionUtil.isNotEmpty(insertIds)){
                LambdaQueryWrapper<ClassCourseTeacherDO> classCourseTeacherDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                classCourseTeacherDOLambdaQueryWrapper.in(ClassCourseTeacherDO::getClassCourseId, insertIds);
                classCourseTeacherMapper.delete(classCourseTeacherDOLambdaQueryWrapper);
            }
            List<ClassCourseTeacherDO> classCourseTeacherDOList = new ArrayList<>();
            if (StringUtils.isNotEmpty(reqVO.getTeacherIdString())) {
                // 将teacherIdString按逗号分隔成教师ID数组
                String[] teacherIdsInsert = reqVO.getTeacherIdString().split(",");
                // 遍历teacherIds数组，为每个ID创建ClassCourseTeacherDO对象
                long sort = 0;
                for (String teacherId : teacherIdsInsert) {
                    ClassCourseTeacherDO teacherDO = new ClassCourseTeacherDO();
                    teacherDO.setClassCourseId(reqVO.getApplyId());
                    teacherDO.setCourseId(before.getCourseId());
                    teacherDO.setSort(sort);
                    try {
                        teacherDO.setTeacherId(Long.parseLong(teacherId));
                    } catch (NumberFormatException e) {
                        // 如果teacherId无法转换为Long，可以处理异常
                        System.err.println("无效的教师ID: " + teacherId);
                    }
                    if(teacherDO.getTeacherId() != null && teacherDO.getClassCourseId() != null){
                        classCourseTeacherDOList.add(teacherDO);
                        sort++;
                    }
                }
            }
            classCourseTeacherMapper.insertBatch(classCourseTeacherDOList);
        }

        // 4. 按优先级选择最终变更类型
        triggeredChanges.stream()
                .min(Comparator.comparingInt(CourseChangeTypeEnum::getCode))
                .ifPresent(finalChangeType -> wrapper.set(ClassCourseDO::getChangeType, finalChangeType.getCode()));

        classCourseService.update(wrapper);
        this.saveBatch(recordList);
        if((LocalDate.now().toString().equals(before.getDate()) ||
                (Objects.nonNull(reqVO.getChangeId()) && LocalDate.now().toString().equals(after.getDate())))
        && !Boolean.TRUE.equals(reqVO.getNotMergeSync())){
            // 刷新签到表,立即提交任务，不等待结果
            CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                    .exceptionally(e -> {
                        log.error(e.getMessage(), e);
                        return null;
                    });
        }
    }

    /**
     * 合班授课同步调课处理
     * @param reqVO 调课请求VO
     * @param before 调课前的课程信息
     * @param changeTime 是否修改时间段
     * @param after 调课后的课程信息
     */
    private void syncMergeClassChange(CourseChangeReqVO reqVO, ClassCourseInfoDTO before,
                                      boolean changeTime, ClassCourseInfoDTO after) {
        // 合班授课同步调课处理 reqVO.getNotMergeSync()防止无限递归调用
        if(before.getIsMerge() && !Boolean.TRUE.equals(reqVO.getNotMergeSync())){
            // 查询申请课程以外其他的所有的合班授课的课程
            List<ClassCourseClassNameRespDTO> applyMregedClassCourseDOList = classCourseMapper.selectMergedClassCourse(reqVO.getApplyId());
            // 班级id->班级名称 Map
            Map<Long, String> classIdNameMap = applyMregedClassCourseDOList.stream()
                    .collect(Collectors.toMap(ClassCourseClassNameRespDTO::getClassId, ClassCourseClassNameRespDTO::getClassName, (k1, k2) -> k1));
            // 合班授课同步调课
            List<CourseChangeReqVO> courseChangeReqVOList = new ArrayList<>();
            // 合班授课的班级的排课id-> 修改时间段后的排课id Map
            Map<Long, Long> classCourseIdMap = new HashMap<>();
            // 如果修改时间段需要，获取合班班级对应时间段的排课
            if (changeTime){
                // 查询时间段对应的课程信息
                // 获取合班授课班级其他目标时间段的课程
                List<Long> mergedClassIdList = applyMregedClassCourseDOList.stream()
                        .map(ClassCourseClassNameRespDTO::getClassId).collect(Collectors.toList());
                String classNames = applyMregedClassCourseDOList.stream().map(ClassCourseClassNameRespDTO::getClassName).collect(Collectors.joining("、"));
                List<ClassCourseDO> targetPeriodClassCourses = new ArrayList<>();
                // 目标时间段排课查询
                if (CollectionUtil.isNotEmpty(mergedClassIdList)) {
                    targetPeriodClassCourses = classCourseMapper.selectClassCourseByClassIdsAndTime(mergedClassIdList,
                            truncateToMinute(after.getBeginTime()), truncateToMinute(after.getEndTime()));
                }
                // 同步班级中冲突的班级
                List<ClassCourseDO> conflictClassCourses = targetPeriodClassCourses.stream()
                        .filter(item -> Boolean.FALSE.equals(item.getIsTemporary()))
                        .collect(Collectors.toList());

                // 若合并授课班级调课目标有已发布的格子
                if (CollUtil.isNotEmpty(conflictClassCourses)) {
                    String conflictClassNames = conflictClassCourses.stream()
                            .map(o -> classIdNameMap.getOrDefault(o.getClassId(), o.getClassId().toString()))
                            .collect(Collectors.joining("、"));
                    // 若有其他班级在该时间段已有课程，则不允许调课
                    throw exception(CLASS_COURSE_MERGED_CHANGE_TIME_CONFLICT, conflictClassNames);
                }

                // 检查某些班级目标格子 是否存在 不存在则记录
                List<String> conflictClassNames = new ArrayList<>();
                for (ClassCourseClassNameRespDTO classNameRespDTO : applyMregedClassCourseDOList) {
                    List<ClassCourseDO> courseDOList = targetPeriodClassCourses.stream()
                            .filter(item -> item.getClassId().equals(classNameRespDTO.getClassId())).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(courseDOList)) {
                        conflictClassNames.add(classNameRespDTO.getClassName());
                        continue;
                    }
                    classCourseIdMap.put(classNameRespDTO.getId(), courseDOList.get(0).getId());
                }
                if (CollectionUtil.isNotEmpty(conflictClassNames)) {
                    throw exception(CLASS_COURSE_MERGED_CHANGE_TIME_CONFLICT, String.join("、", conflictClassNames));
                }
            }
            if (CollectionUtil.isNotEmpty(applyMregedClassCourseDOList)){
                for (ClassCourseClassNameRespDTO classNameRespDTO : applyMregedClassCourseDOList) {
                    CourseChangeReqVO courseChangeReqVO = new CourseChangeReqVO();
                    BeanUtil.copyProperties(reqVO, courseChangeReqVO);
                    // 设置同步的班级信息
                    courseChangeReqVO.setApplyId(classNameRespDTO.getId());
                    courseChangeReqVO.setClassId(classNameRespDTO.getClassId());
                    courseChangeReqVO.setClassName(classNameRespDTO.getClassName());
                    // 已经同步调课了，不再重复同步调课处理，防止无限递归调用
                    courseChangeReqVO.setNotMergeSync(true);
                    // 修改时间段
                    if (changeTime){
                        courseChangeReqVO.setChangeId(classCourseIdMap.get(classNameRespDTO.getId()));
                    }
                    courseChangeReqVOList.add(courseChangeReqVO);
                }

            }
            courseChangeReqVOList.forEach(item -> courseChangeService.change(item));
        }
    }

    @Override
    public List<Object> getFreeTime(Long classId) {
//        PlanDO plan = planService.getPlanByClassId(classId);
//        if(Objects.isNull(plan)){
//            return null;
//        }
        List<ClassCourseDO> list = classCourseService.list(new LambdaQueryWrapperX<ClassCourseDO>()
//                .eq(ClassCourseDO::getPlanId, plan.getId())
                .eq(ClassCourseDO::getClassId, classId)
                .orderByAsc(ClassCourseDO::getBeginTime)
        );
        List<Object> res = new ArrayList<>();
        list.forEach(item -> {
            Map<String, String> map = new HashMap<>();
            map.put("date", item.getDate());
            map.put("period", item.getPeriod());
            map.put("time", getTime(item.getBeginTime(), item.getEndTime()));
            map.put("changeId", item.getId().toString());
            map.put("isFree", item.getIsTemporary()?"1":"0");
            res.add(map);
        });
        return res;
    }

    private String getDayTime(String date, String period, LocalDateTime time1, LocalDateTime time2){
        return date +" "+ PeriodEnum.getDescByPeriod(Integer.parseInt(period)) + getTime(time1, time2);
    }

    private String getTime(LocalDateTime time1, LocalDateTime time2){
        if(Objects.isNull(time1) || Objects.isNull(time2)){
            return "";
        }
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(FORMAT_HOUR_MINUTE);
        return time1.format(timeFormatter) + "-" + time2.format(timeFormatter);
    }

    private CourseChangeDO getChangeRecordInfo(CourseChangeBaseVO reqVO, ClassCourseInfoDTO before, ClassCourseInfoDTO after) {
        CourseChangeDO change = new CourseChangeDO();
        change.setClassId(reqVO.getClassId());
        change.setClassName(reqVO.getClassName());
        change.setChangeTime(LocalDateTime.now());
        change.setChangeUserId(getLoginUserId());
        change.setChangeUserName(userApi.getUser(getLoginUserId()).getCheckedData().getNickname());
        change.setChangeReason(reqVO.getChangeReason());
        change.setClassInfoBefore(getClassInfo(before.getCourseId(),before.getCourseName(),before.getTeacherName(),before.getClassroom(),
                before.getDate(),before.getPeriod(),before.getBeginTime(),before.getEndTime()));
        change.setClassInfoAfter(getClassInfo(after.getCourseId(),after.getCourseName(),after.getTeacherName(),after.getClassroom(),
                after.getDate(),after.getPeriod(),after.getBeginTime(),after.getEndTime()));
        //附带调课对象的课表id
        change.setClassCourseId(before.getId());
        return change;
    }

    private String getClassInfo(Long courseId, String courseName, String teacherName, String classroom,
                                String date, String period, LocalDateTime beginTime, LocalDateTime endTime){
        if(Objects.isNull(courseId)){
            return "";
        }
        if(Objects.equals(-1L, courseId)){
            return "选修课@#-@#-@#" + getDayTime(date, period, beginTime, endTime);
        }
        if(Objects.isNull(teacherName)){
            teacherName = "-";
        }
        if(Objects.isNull(classroom)){
            classroom = "-";
        }
        return courseName + "@#" + teacherName + "@#" + classroom + "@#" + getDayTime(date, period, beginTime, endTime);
    }

    public static LocalTime[] convertToLocalTime(String timeRange) {
        String[] parts = timeRange.split("-");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        LocalTime startTime = LocalTime.parse(parts[0], formatter);
        LocalTime endTime = LocalTime.parse(parts[1], formatter);

        return new LocalTime[]{startTime, endTime};
    }

    private LambdaUpdateWrapper<ClassCourseDO> emptyClassCourse(Long id) {
        return new LambdaUpdateWrapper<ClassCourseDO>()
                .set(ClassCourseDO::getCourseId, null)
                .set(ClassCourseDO::getTeacherId, null)
                .set(ClassCourseDO::getTeacherIdString, null)
                .set(ClassCourseDO::getClassroomId, null)
                .set(ClassCourseDO::getIsTemporary, true)
                .set(ClassCourseDO::getIsMerge, false)
                .set(ClassCourseDO::getIsChange, false)
                .eq(ClassCourseDO::getId, id);
    }

    /**
     * 调课时对已有排课的格子替换为另外选的课程
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateClassCourse(CourseChangeUpdateReqVO updateReqVO) {
        // TODO: 选修课发布后替换成别的课逻辑处理

        // 校验存在
        classCourseService.validateClassCourseExists(updateReqVO.getId());
        ClassCourseInfoDTO before = courseChangeMapper.getClassInfo(updateReqVO.getId());
        // 更新
        ClassCourseDO updateObj = ClassCourseConvert.INSTANCE.convertCourseChangeVOToDO(updateReqVO);
        //设置数据更新时间为当前时间
        updateObj.setUpdateTime(LocalDateTime.now());
        //设置是否调课
        updateObj.setIsChange(true);
        //将teacherIdString中的第一位老师放置于teacherId字段
        if (StringUtils.isNotEmpty(updateObj.getTeacherIdString())) {
            // 获取逗号分隔的教师ID字符串并提取第一个ID
            String[] teacherIds = updateObj.getTeacherIdString().split(",");
            // 将第一个教师ID赋值给 teacherId 字段
            if (teacherIds.length > 0) {
                try {
                    updateObj.setTeacherId(Long.parseLong(teacherIds[0]));
                } catch (NumberFormatException e) {
                    // 如果解析失败，处理异常
                    log.error("无法解析教师ID: {}", teacherIds[0]);
                }
            }
        }else{
            //活动课无教师手动置空
            LambdaUpdateWrapper<ClassCourseDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ClassCourseDO::getId, updateReqVO.getId())
                    .set(ClassCourseDO::getTeacherId, null)
                    .set(ClassCourseDO::getClassroomId, null)
                    .set(ClassCourseDO::getTeacherIdString, null);
            classCourseMapper.update(null, updateWrapper);
        }
        //删除原本自带的教师关联信息后,新增现有的关联信息
        List<ClassCourseDO> classCourseDOList = new ArrayList<>();
        classCourseDOList.add(updateObj);
        classCourseService.changeClassCourseTeacher(classCourseDOList);
        //批量排课时设置数据更新时间为当前时间
        updateObj.setUpdateTime(LocalDateTime.now());
        classCourseMapper.updateById(updateObj);

        //增加调课记录
        ClassCourseInfoDTO after = courseChangeMapper.getClassInfo(updateReqVO.getId());
        CourseChangeBaseVO reqVO = new CourseChangeBaseVO();
        reqVO.setClassId(before.getClassId());
        reqVO.setClassName(before.getClassName());
        CourseChangeDO changeRecord = getChangeRecordInfo(reqVO, before, after);
        changeRecord.setChangeType(CourseChangeTypeEnum.EXCHANGE.getCode());
        courseChangeMapper.insert(changeRecord);

        //刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    /**
     * 根据班级课程ID获取课程变更信息
     * 此方法用于查询特定班级课程的变更详情，以便用户了解课程在变更前后的具体信息
     *
     * @param classCourseId 班级课程ID，用于标识特定的班级课程记录
     * @return CourseChangeRespVO 返回课程变更响应对象，包含变更前后的课程详情如果无变更信息，则返回null
     */
    @Override
    public CourseChangeRespVO getCourseChangeInfoByClassCourseId(Long classCourseId){
        List<CourseChangeRespVO> courseChangeRespVOS = courseChangeMapper.selectInfoByClassCourseId(classCourseId);
        if(courseChangeRespVOS.isEmpty()){
           return null;
        }
        CourseChangeRespVO result = courseChangeRespVOS.get(0);
        result.setClassInfoBeforeDetail(getClassInfoDetail(result.getClassInfoBefore()));
        result.setClassInfoAfterDetail(getClassInfoDetail(result.getClassInfoAfter()));
        return result;
    }

}
