package com.unicom.swdx.module.edu.service.courses;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.write.handler.WriteHandler;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.excel.core.handler.SelectedColumnWriteHandler;
import com.unicom.swdx.framework.excel.core.util.ExcelDataWriteDTO;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.LoginUser;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.*;
import com.unicom.swdx.module.edu.convert.courses.CoursesConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses.ElectiveReleaseCoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.teachercourseinformation.TeacherCourseInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.enums.courses.CoursesDictEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesStatusEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.electiverelease.ClassDayPeriodEnum;
import com.unicom.swdx.module.edu.utils.excel.ExcelImportResultRespVO;
import com.unicom.swdx.module.edu.utils.tree.TreeDataUtil;
import com.unicom.swdx.module.edu.utils.tree.dto.TreeNodeDTO;
import com.unicom.swdx.module.infra.enums.DictTypeConstants;
import com.unicom.swdx.module.system.api.businesscenter.BusinessCenterApi;
import com.unicom.swdx.module.system.api.businesscenter.dto.DeptSimpleRespDTO;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.*;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;
import static com.unicom.swdx.module.edu.utils.upload.UploadFileUtil.UploadExcel;

/**
 * 课程库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CoursesServiceImpl implements CoursesService {

    @Resource
    private CoursesMapper coursesMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private BusinessCenterApi businessCenterApi;

    @Resource
    private TeacherCourseInformationMapper teacherCourseInformationMapper;

    @Resource
    private ElectiveReleaseCoursesMapper electiveReleaseCoursesMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    private static final int THREAD_POOL_SIZE = 10;

    private final ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);


    /**
     * 创建课程 (1-专题课 2-选修课 3-教学活动)
     *
     * @param createReqVO 创建信息
     * @return 新增课程ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCourses(CoursesCreateReqVO createReqVO) {
        // 校验课程字段
        validateCourseFields(createReqVO, null);
        // 插入课程信息
        CoursesDO courses = CoursesConvert.INSTANCE.convert(createReqVO);
        // insert操作会赋值id
        coursesMapper.insert(courses);
        List<Long> teacherIds = createReqVO.getTeacherIds();
        // 关联授课教师
        if (Objects.nonNull(teacherIds) && !teacherIds.isEmpty()) {
            List<Long> distinctTeacherIds = teacherIds.stream().distinct().collect(Collectors.toList());
            // 校验授课教师是否全部存在
            List<TeacherInformationDO> teacherInformationDOS = teacherInformationMapper.selectBatchIds(distinctTeacherIds);
            // 根据id查询到的教师数量是否一致
            if (distinctTeacherIds.size() != teacherInformationDOS.size()) {
                throw exception(COURSES_TEACHER_NOT_EXISTS);
            }
            // 插入关联关系
            ArrayList<TeacherCourseInformationDO> insertTeacherCourseInformation = new ArrayList<>();
            for (Long teacherId : createReqVO.getTeacherIds()) {
                insertTeacherCourseInformation.add(TeacherCourseInformationDO.builder()
                        .coursesId(courses.getId())
                        .teacherId(teacherId)
                        .build());
            }
            teacherCourseInformationMapper.insertBatch(insertTeacherCourseInformation);
        }
        // 返回
        return courses.getId();
    }

    private void validateCourseFields(CoursesBaseVO createReqVO, Long id) {
        // 如果不是教学活动
        if (!CoursesTypeEnum.TEACHING_ACTIVITY.getType().equals(createReqVO.getCoursesType())) {
            // 校验课程分类、教学形式、状态 必填项
            if (Objects.isNull(createReqVO.getThemeId())) {
                if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(createReqVO.getCoursesType())) {
                    throw exception(COURSES_TOPIC_THEME_ID_REQUIRED);
                } else {
                    throw exception(COURSES_OPTIONAL_THEME_ID_REQUIRED);
                }
            }
            if (Objects.isNull(createReqVO.getEducateFormId())) {
                throw exception(COURSES_EDUCATE_FORM_ID_REQUIRED);
            }
            if (Objects.isNull(createReqVO.getStatus())) {
                if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(createReqVO.getCoursesType())) {
                    throw exception(COURSES_TOPIC_STATUS_REQUIRED);
                } else {
                    throw exception(COURSES_OPTIONAL_STATUS_REQUIRED);
                }
            }
        }
        // 校验名称
        if (StringUtils.isBlank(createReqVO.getName()) || createReqVO.getName().trim().length() > 50) {
            throw exception(COURSES_NAME_REQUIRED, CoursesTypeEnum.getDescByType(createReqVO.getCoursesType()));
        }
        createReqVO.setName(createReqVO.getName().trim());
        List<CoursesDO> coursesDOS = coursesMapper.selectByNameAndType(createReqVO.getName(), createReqVO.getCoursesType(), id);
        if (!coursesDOS.isEmpty()) {
            if (!CoursesTypeEnum.TOPIC_COURSE.getType().equals(createReqVO.getCoursesType())) {
                // 选修课、教学活动课重名 抛出异常
                throw exception(COURSES_NAME_DUPLICATE);
            } else {
                List<Long> baseTeacherIds = createReqVO.getTeacherIds();
                Set<Long> baseTeacherIdSet;
                if (Objects.isNull(baseTeacherIds)){
                    baseTeacherIdSet = new HashSet<>();
                }else {
                    baseTeacherIdSet = new HashSet<>(baseTeacherIds);
                }
                List<TeacherCourseInformationDO> teacherCourseInformationDOS = teacherCourseInformationMapper.selectListByCoursesIds(
                        coursesDOS.stream().map(CoursesDO::getId).collect(Collectors.toList()));
                for (CoursesDO coursesDO : coursesDOS) {
                    // 查询当前课程已有的授课教师关联关系
                    Set<Long> existTeacherIdsSet = teacherCourseInformationDOS.stream().filter(o->coursesDO.getId().equals(o.getCoursesId()))
                            .map(TeacherCourseInformationDO::getTeacherId).collect(Collectors.toSet());
                    // 如果即同名又有相等的教师id 则提示重复
                    if (baseTeacherIdSet.equals(existTeacherIdsSet)){
                        throw exception(COURSES_NAME_DUPLICATE);
                    }
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCourses(CoursesUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateCoursesExists(updateReqVO.getId());
        // 校验课程字段
        validateCourseFields(updateReqVO, updateReqVO.getId());
        // 更新课程信息
        CoursesDO updateObj = CoursesConvert.INSTANCE.convert(updateReqVO);
        // 关联授课教师
        if (Objects.nonNull(updateReqVO.getTeacherIds()) && !updateReqVO.getTeacherIds().isEmpty()) {
            // 去重
            Set<Long> processTeacherIds = new HashSet<>(updateReqVO.getTeacherIds());
            // 校验授课教师是否全部存在
            List<TeacherInformationDO> teacherInformationDOS = teacherInformationMapper.selectBatchIds(processTeacherIds);
            // 根据id查询到的教师数量是否一致
            if (processTeacherIds.size() != teacherInformationDOS.size()) {
                throw exception(COURSES_TEACHER_NOT_EXISTS);
            }
            // 查询当前课程已有的授课教师关联关系
            List<TeacherCourseInformationDO> informationList = teacherCourseInformationMapper.selectListByCoursesId(updateReqVO.getId());
            // 教师ID -> 关联关系ID列表
            Map<Long, Long> teacherId2InformationIdsMap = informationList.stream()
                    .collect(Collectors.toMap(TeacherCourseInformationDO::getTeacherId, TeacherCourseInformationDO::getId));
            // 已有的关联教师ID列表
            Set<Long> existTeacherIds = informationList.stream().map(TeacherCourseInformationDO::getTeacherId).collect(Collectors.toSet());
            // 获得需要删除的教师 ID
            Set<Long> toBeDeletedIds = existTeacherIds.stream()
                    .filter(id -> !processTeacherIds.contains(id))
                    .map(teacherId2InformationIdsMap::get)
                    .collect(Collectors.toSet());
            if (!toBeDeletedIds.isEmpty()) {
                teacherCourseInformationMapper.deleteBatchIds(toBeDeletedIds);
            }
            // 获得需要新增的教师 ID
            Set<Long> toBeInsertedTeacherIds = processTeacherIds.stream()
                    .filter(id -> !existTeacherIds.contains(id))
                    .collect(Collectors.toSet());

            List<TeacherCourseInformationDO> toBeInsert = new ArrayList<>();
            toBeInsertedTeacherIds.forEach(teacherId -> toBeInsert.add(TeacherCourseInformationDO.builder()
                    .coursesId(updateReqVO.getId())
                    .teacherId(teacherId)
                    .build()));
            teacherCourseInformationMapper.insertBatch(toBeInsert);
        }
        coursesMapper.updateById(updateObj);
    }

    /**
     * 课程库分页
     *
     * @param pageReqVO 分页查询
     * @return 分页列表
     */
    @Override
    public PageResult<CoursesRespVO> getCoursesPage(CoursesPageReqVO pageReqVO, HttpServletRequest request) {
        IPage<CoursesRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        List<CoursesRespVO> pageResultList = coursesMapper.selectPageByReqVO(page, pageReqVO);
        // "，"分割字符串转列表
        setTeacherInfo(pageResultList);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(pageReqVO.getIsSerialDesc(),
                page.getTotal(),
                pageReqVO,
                pageResultList.size());
        for (int i = 0; i < pageResultList.size(); i++) {
            pageResultList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        packageDictData(pageResultList, request);
        return new PageResult<>(pageResultList, page.getTotal());
    }

    private void setTeacherInfo(List<CoursesRespVO> pageResultList) {
        pageResultList.forEach(vo -> {
            String teacherIdListStr = vo.getTeacherIdListStr();
            if (StringUtils.isNotBlank(teacherIdListStr)) {
                String[] teacherNames = teacherIdListStr.split("，");
                vo.setTeacherIdList(Arrays.stream(teacherNames).map(Long::valueOf).collect(Collectors.toList()));
            }
            String teacherNameListStr = vo.getTeacherNameList();
            if (StringUtils.isNotBlank(teacherNameListStr)) {
                String[] teacherNames = teacherNameListStr.split("，");
                vo.setTeacherNameListList(Arrays.stream(teacherNames).collect(Collectors.toList()));
            }
        });
    }

    /**
     * 获得一个专题课的授课记录
     *
     * @param pageReqVO 查询条件
     * @return 授课记录分页
     */
    @Override
    public PageResult<CoursesTeachingRecordRespVO> getPageForTeachingRecord(CoursesTeachingRecordReqVO pageReqVO) {
        IPage<CoursesTeachingRecordRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        List<CoursesTeachingRecordRespVO> pageResultList = coursesMapper.selectPageForCourseTeachingRecord(page, pageReqVO,
                LocalDateTime.now());
        // 组装授课时间字符串
        pageResultList.forEach(vo -> {
            String classDuration = vo.getClassDate().split("\\.")[0] +
                    " " + ClassDayPeriodEnum.getDescByPeriod(vo.getDayPeriod()) +
                    " " + DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                    " ~ " + DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE);
            vo.setClassDuration(classDuration);
        });
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(pageReqVO.getIsSerialDesc(),
                page.getTotal(),
                pageReqVO,
                pageResultList.size());
        for (int i = 0; i < pageResultList.size(); i++) {
            pageResultList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(pageResultList, page.getTotal());
    }

    /**
     * 导出一个专题课的授课记录
     *
     * @param pageReqVO 查询条件
     * @return 授课记录列表
     */
    @Override
    public List<CoursesTeachingRecordTopicExcelVO> getExportTopicTeachingRecordExcel(CoursesTeachingRecordReqVO pageReqVO) {
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(Integer.MAX_VALUE);
        List<CoursesTeachingRecordRespVO> list = this.getPageForTeachingRecord(pageReqVO).getList();
        return CoursesConvert.INSTANCE.convertList08(list);
    }

    /**
     * 导出勾选下的多个专题课的授课记录
     *
     * @param pageVO 页面课程查询条件
     * @return 授课记录列表
     */
    @Override
    public List<CoursesTeachingRecordTopicExcelVO> getExportBatchTopicTeachingRecordExcel(CoursesPageReqVO pageVO) {
        // 如果是条件筛选导出 则先查询课程在 设置id集合
        if (Objects.isNull(pageVO.getIds()) || pageVO.getIds().isEmpty()) {
            pageVO.setPageNo(1);
            pageVO.setPageSize(Integer.MAX_VALUE);
            IPage<CoursesRespVO> page = MyBatisUtils.buildPage(pageVO);
            List<CoursesRespVO> pageResultList = coursesMapper.selectPageByReqVO(page, pageVO);
            pageVO.setIds(pageResultList.stream().map(CoursesRespVO::getId).collect(Collectors.toList()));
        }
        // 根据排序规则和课程id查询授课记录列表
        List<CoursesTeachingRecordRespVO> list = coursesMapper.selectListForTeachingRecordByReqVO(pageVO,
                LocalDateTime.now());
        // 组装授课时间字符串
        list.forEach(vo -> {
            String classDuration = vo.getClassDate().split("\\.")[0] +
                    " " + ClassDayPeriodEnum.getDescByPeriod(vo.getDayPeriod()) +
                    " " + DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                    " ~ " + DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE);
            vo.setClassDuration(classDuration);
        });
        return CoursesConvert.INSTANCE.convertList08(list);
    }


    /**
     * 获得一个选修课的授课记录
     *
     * @param pageVO 查询条件
     * @return 授课记录分页
     */
    @Override
    public PageResult<CoursesTeachingRecordRespVO> getPageForElectiveTeachingRecord(CoursesTeachingRecordReqVO pageVO) {
        IPage<CoursesTeachingRecordRespVO> page = MyBatisUtils.buildPage(pageVO);
        List<CoursesTeachingRecordRespVO> pageResultList = electiveReleaseCoursesMapper.selectPageForCourseTeachingRecord(page, pageVO,
                LocalDateTime.now());
        // 组装授课时间字符串
        pageResultList.forEach(vo -> {
            vo.setClassDate(DateUtils.format(DateUtils.parseLocalDateTime(vo.getClassDate().split("\\.")[0],
                            FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND),
                    FORMAT_YEAR_MONTH_DAY));
            String classDuration = vo.getClassDate().split("\\.")[0] +
                    " " + ClassDayPeriodEnum.getDescByPeriod(vo.getDayPeriod()) +
                    " " + DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                    " ~ " + DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE);
            vo.setClassDuration(classDuration);
        });
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(pageVO.getIsSerialDesc(),
                page.getTotal(),
                pageVO,
                pageResultList.size());
        for (int i = 0; i < pageResultList.size(); i++) {
            pageResultList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(pageResultList, page.getTotal());
    }

    /**
     * 导出一个选修课的授课记录
     *
     * @param pageVO 查询条件
     * @return 授课记录列表
     */
    @Override
    public List<CoursesTeachingRecordElectiveExcelVO> getExportElectiveTeachingRecordExcel(CoursesTeachingRecordReqVO pageVO) {
        pageVO.setPageNo(1);
        pageVO.setPageSize(Integer.MAX_VALUE);
        List<CoursesTeachingRecordRespVO> list = this.getPageForElectiveTeachingRecord(pageVO).getList();
        return CoursesConvert.INSTANCE.convertList09(list);
    }

    /**
     * 导出勾选下的多个选修课的授课记录
     *
     * @param pageVO 页面课程查询条件
     * @return 授课记录列表
     */
    @Override
    public List<CoursesTeachingRecordElectiveExcelVO> getExportBatchElectiveTeachingRecordExcel(CoursesPageReqVO pageVO) {
        // 如果是条件筛选导出 则先查询课程在 设置id集合
        if (CollectionUtils.isEmpty(pageVO.getIds())) {
            pageVO.setPageNo(1);
            pageVO.setPageSize(Integer.MAX_VALUE);
            IPage<CoursesRespVO> page = MyBatisUtils.buildPage(pageVO);
            List<CoursesRespVO> pageResultList = coursesMapper.selectPageByReqVO(page, pageVO);
            pageVO.setIds(pageResultList.stream().map(CoursesRespVO::getId).collect(Collectors.toList()));
        }
        // 根据排序规则和课程id查询授课记录列表
        List<CoursesTeachingRecordRespVO> list = electiveReleaseCoursesMapper.selectListForTeachingRecordByReqVO(pageVO,
                LocalDateTime.now());
        // 组装授课时间字符串
        list.forEach(vo -> {
            vo.setClassDate(DateUtils.format(DateUtils.parseLocalDateTime(vo.getClassDate().split("\\.")[0], FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND),
                    FORMAT_YEAR_MONTH_DAY));
            String classDuration = vo.getClassDate().split("\\.")[0] +
                    " " + ClassDayPeriodEnum.getDescByPeriod(vo.getDayPeriod()) +
                    " " + DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                    " ~ " + DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE);
            vo.setClassDuration(classDuration);
        });
        return CoursesConvert.INSTANCE.convertList09(list);
    }

    /**
     * 获得课程库列表
     *
     * @param request 请求
     * @param reqVO   筛选条件
     * @return 课程库列表
     */
    @Override
    public List<CoursesRespVO> getCoursesListByReqVO(HttpServletRequest request, CoursesPageReqVO reqVO) {
        List<CoursesRespVO> coursesRespVOS = coursesMapper.selectListByReqVO(reqVO);
        // 设置对应授课教师信息
        // "，"分割字符串转列表
        setTeacherInfo(coursesRespVOS);
        packageDictData(coursesRespVOS, request);
        return coursesRespVOS;
    }

    /**
     * 批量删除课程库 多选或条件筛选
     *
     * @param pageReqVO 筛选条件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteCourses(CoursesPageReqVO pageReqVO) {
        List<Long> toBeDeleteIds;
        // 如果是勾选删除
        if (Objects.nonNull(pageReqVO.getIds()) && !pageReqVO.getIds().isEmpty()) {
            toBeDeleteIds = pageReqVO.getIds();
        }
        // 如果是条件筛选删除
        else {
            List<CoursesRespVO> coursesRespVOS = coursesMapper.selectListByReqVO(pageReqVO);
            toBeDeleteIds = coursesRespVOS.stream().map(CoursesRespVO::getId).collect(Collectors.toList());
        }
        // 校验课程是否已经排课 排课后无法删除
        if (CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(pageReqVO.getCoursesType())) {
            // 如果课程是选修课
            List<ElectiveReleaseCoursesDO> list = electiveReleaseCoursesMapper.selectCourseByCourseIdList(toBeDeleteIds);
            if (!list.isEmpty()) {
                // 报错 - 课程已排课不可删除
                throw exception(COURSE_HAS_BEEN_RELEASED_DELETE_FAILURE);
            }
        } else {
            // 如果是专题课、教学活动课
            List<ClassCourseDO> list = classCourseMapper.selectListByCourseIdList(toBeDeleteIds);
            if (!list.isEmpty()) {
                // 报错 - 课程已排课不可删除
                throw exception(COURSE_HAS_BEEN_RELEASED_DELETE_FAILURE);
            }
        }
        // 删除课程信息
        if (!toBeDeleteIds.isEmpty()) {
            coursesMapper.deleteBatchIds(toBeDeleteIds);
        }
        // 删除授课教师关联信息
        if (!toBeDeleteIds.isEmpty()) {
            teacherCourseInformationMapper.deleteByCoursesIdList(toBeDeleteIds);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCourses(Long id) {
        // 校验存在
        CoursesDO coursesDO = this.validateCoursesExists(id);
        // 校验课程是否已经排课 排课后无法删除
        if (CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(coursesDO.getCoursesType())) {
            // 如果课程是选修课
            List<ElectiveReleaseCoursesDO> list = electiveReleaseCoursesMapper.selectCourseByCourseIdList(Collections.singletonList(id));
            if (!list.isEmpty()) {
                // 报错 - 课程已排课不可删除
                throw exception(COURSE_HAS_BEEN_RELEASED_DELETE_FAILURE);
            }
        } else {
            // 如果是专题课、教学活动课
            List<ClassCourseDO> list = classCourseMapper.selectListByCourseIdList(Collections.singletonList(id));
            if (!list.isEmpty()) {
                // 报错 - 课程已排课不可删除
                throw exception(COURSE_HAS_BEEN_RELEASED_DELETE_FAILURE);
            }
        }
        // 删除课程
        coursesMapper.deleteById(id);
        // 删除授课教师关联信息
        teacherCourseInformationMapper.deleteByCoursesId(id);
    }

    /**
     * 导出课程库 Excel
     *
     * @param pageReqVO 查询条件
     */
    @Override
    public List<CoursesExcelVO> getExportCoursesExcel(CoursesPageReqVO pageReqVO, HttpServletRequest request) {
        // 通过勾选或者条件获取课程库信息
        List<CoursesRespVO> coursesRespVOList = getExportDataListByIdsOrReqVO(pageReqVO, request);

        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(ListUtil.of(CoursesDictEnum.TEACHING_METHOD.getType())).getCheckedData();

        // 转换成导出对象
        List<CoursesExcelVO> coursesExcelVOList = CoursesConvert.INSTANCE.convertList02(coursesRespVOList);

        for (int i = 0; i < coursesExcelVOList.size(); i++) {
            int finalI = i;
            try {
                coursesExcelVOList.get(i).setTeachingMethod(dictData.stream().filter(Objects::nonNull).filter(it-> it.getId()== coursesRespVOList.get(finalI).getTeachingMethodId()).findFirst().get().getLabel());
            }catch (Exception e){
                e.printStackTrace();
            }

        }

        // 课程状态字符串转换
        for (int i = 0; i < coursesExcelVOList.size(); i++) {
            coursesExcelVOList.get(i).setStatusName(CoursesStatusEnum.getDescByStatus(coursesRespVOList.get(i).getStatus()));
        }
        return coursesExcelVOList;
    }

    /**
     * 导出教学活动 Excel
     *
     * @param pageVO 查询条件
     * @return 导出数据
     */
    @Override
    public List<CoursesActivityExcelVO> getExportActivityCoursesExcel(CoursesPageReqVO pageVO, HttpServletRequest request) {
        pageVO.setCoursesType(CoursesTypeEnum.TEACHING_ACTIVITY.getType());
        // 通过勾选或者条件获取课程库信息
        List<CoursesRespVO> coursesRespVOList = getExportDataListByIdsOrReqVO(pageVO, request);
        // 转换成导出对象
        List<CoursesActivityExcelVO> coursesExcelVOList = CoursesConvert.INSTANCE.convertList03(coursesRespVOList);
        // 活动类型字符串转换
        // 获取所有教学形式字典
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(Collections.singletonList(CoursesDictEnum.EDU_ACTIVITY.getType())).getCheckedData();
        // value -> label
        Map<String, String> activityTypeMap = dictDataList.stream().collect(Collectors.toMap(DictDataRespDTO::getValue, DictDataRespDTO::getLabel));
        for (int i = 0; i < coursesExcelVOList.size(); i++) {
            Long activityType = coursesRespVOList.get(i).getActivityType();
            if (Objects.nonNull(activityType)) {
                coursesExcelVOList.get(i).setActivityTypeName(activityTypeMap.get(activityType.toString()));
            }
        }
        return coursesExcelVOList;
    }

    /**
     * 导出选修课库模板
     *
     * @param request  请求
     * @param response 响应
     */
    @Override
    public void exportOptionalTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        exportTemplate(request, response, CoursesTypeEnum.OPTIONAL_COURSE.getType(), CoursesOptionalImportExcelVO.class);
    }

    /**
     * 导出专题库模板
     *
     * @param request  请求
     * @param response 响应
     */
    @Override
    public void exportTopicTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        exportTemplate(request, response, CoursesTypeEnum.TOPIC_COURSE.getType(), CoursesTopicImportExcelVO.class);
    }

    /**
     * 导出专题、选修库模板
     *
     * @param request     请求
     * @param response    响应
     * @param coursesType 课程类型
     */
    public <T> void exportTemplate(HttpServletRequest request, HttpServletResponse response, Integer coursesType, Class<T> head) throws IOException {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }
        // 获取教学形式、教学方式、课程分类所有字典选项
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.EDUCATE_FORM.getType(), CoursesDictEnum.TEACHING_METHOD.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.THEME.getType())).getCheckedData();
        List<DictDataRespDTO> themeDictData = dictDataList.stream()
                .filter(dictDataRespDTO -> dictDataRespDTO.getDictType().equals(CoursesDictEnum.getThemeTypeByCoursesType(coursesType)))
                .collect(Collectors.toList());
        List<DictDataRespDTO> educateFormData = dictDataList.stream()
                .filter(dictDataRespDTO -> dictDataRespDTO.getDictType().equals(CoursesDictEnum.EDUCATE_FORM.getType()))
                .collect(Collectors.toList());
        List<DictDataRespDTO> teachingMethodData = dictDataList.stream()
                .filter(dictDataRespDTO -> dictDataRespDTO.getDictType().equals(CoursesDictEnum.TEACHING_METHOD.getType()))
                .collect(Collectors.toList());
        // 获取所有课程分类的完整路径列表
        List<String> themeLabelList = TreeDataUtil.generateSortedFullPathList(packageDictDataAsTreeNodeDTOList(themeDictData));
        // 获取所有教学形式的完整路径列表
        List<String> educateFormLabelList = TreeDataUtil.generateSortedFullPathList(packageDictDataAsTreeNodeDTOList(educateFormData));
        // 指定教学方式的固定值列表，而不是从数据库获取
        List<String> teachingMethodLabelList = Arrays.asList("讲授式", "案例式", "研讨式", "访谈式", "体验式", "模拟式", "互动式", "行动学习",
                "现场教学", "其他");
        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request)).getCheckedData();
        // 获取所有管理部门的完整路径列表
        List<String> deptLabelList = TreeDataUtil.generateSortedFullPathList(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单 deptLabelList:{}", deptLabelList);

        // 获取当前所有校内校外教师姓名列表
        List<String> teacherNameList = teacherInformationMapper.selectNameList();
        List<CoursesTeacherExcelVO> teacherExcelVOS = teacherNameList.stream().map(CoursesTeacherExcelVO::new).collect(Collectors.toList());

        // 多页sheet导出配置
        List<ExcelDataWriteDTO<?>> excelDataWriteDTOS = new ArrayList<>();

        // 生成下拉列表Map关系 CoursesImportExcelVO 列索引值 ->  下拉框内容列表
        Map<Integer, List<String>> dictDataMap = new HashMap<>();
        dictDataMap.put(1, themeLabelList);
        dictDataMap.put(2, educateFormLabelList);
        dictDataMap.put(3, teachingMethodLabelList);
        dictDataMap.put(4, deptLabelList);

        // 创建下拉框实现Handler 并传入数据
        // 选中提示
        Map<Integer, String> columnPromptsMap = new HashMap<>();
        columnPromptsMap.put(4, "多个授课教师姓名请用“，”分隔。例如：张三，李四");
        WriteHandler selectedColumnWriteHandler = new SelectedColumnWriteHandler(dictDataMap, columnPromptsMap);
        List<T> list = new ArrayList<>();

        // 一页sheet
        ExcelDataWriteDTO<T> excelDataWriteDTO = new ExcelDataWriteDTO<>();
        excelDataWriteDTO.setSheetName("信息");
        excelDataWriteDTO.setHead(head);
        excelDataWriteDTO.setData(list);
        // 下拉WriteHandler
        excelDataWriteDTO.setWriteHandlerList(Collections.singletonList(selectedColumnWriteHandler));

        // 1.模板sheet页
        excelDataWriteDTOS.add(excelDataWriteDTO);
        // 2.授课教师页
        ExcelDataWriteDTO<CoursesTeacherExcelVO> teacherNameExcelDataWriteDTO = new ExcelDataWriteDTO<>();
        teacherNameExcelDataWriteDTO.setSheetName("授课教师");
        teacherNameExcelDataWriteDTO.setHead(CoursesTeacherExcelVO.class);
        teacherNameExcelDataWriteDTO.setData(teacherExcelVOS);
        excelDataWriteDTOS.add(teacherNameExcelDataWriteDTO);

        // 插入模板
        ExcelUtils.write(response, CoursesTypeEnum.getDescByType(coursesType) + "库导入模板.xlsx",
                excelDataWriteDTOS);
    }

    /**
     * 导出教学活动模板
     *
     * @param request  请求
     * @param response 响应
     */
    @Override
    public void exportActivityTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }
        // 获取所有教学形式字典
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(Collections.singletonList(CoursesDictEnum.EDU_ACTIVITY.getType())).getCheckedData();
        // 教学活动所有类型
        List<String> activityTypeList = dictDataList.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());

        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request)).getCheckedData();
        // 获取所有管理部门的完整路径列表
        List<String> deptLabelList = TreeDataUtil.generateSortedFullPathList(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单 deptLabelList:{}", deptLabelList);
        // 生成下拉列表Map关系 CoursesImportExcelVO 列索引值 ->  下拉框内容列表
        Map<Integer, List<String>> dictDataMap = new HashMap<>();
        dictDataMap.put(1, activityTypeList);
        dictDataMap.put(2, deptLabelList);
        List<CoursesActivityImportExcelVO> list = new ArrayList<>();
        ExcelUtils.write(response, "教学活动导入模板.xlsx",
                "信息",
                CoursesActivityImportExcelVO.class,
                list, // 模板无数据
                dictDataMap, // 下拉
                null); // 忽略错误信息列
    }

    /**
     * 导入选修课
     *
     * @param request                       请求
     * @param coursesOptionalImportExcelVOS 导入数据
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExcelImportResultRespVO importOptionalCourses(HttpServletRequest request, List<CoursesOptionalImportExcelVO> coursesOptionalImportExcelVOS) throws InterruptedException, IOException {
        List<CoursesTopicImportResultExcelVO> list = CoursesConvert.INSTANCE.convertList07(coursesOptionalImportExcelVOS);
        return this.importCourses(request, list, CoursesTypeEnum.OPTIONAL_COURSE.getType());
    }

    /**
     * 导入专题课
     *
     * @param request                    请求
     * @param coursesTopicImportExcelVOS 导入数据
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExcelImportResultRespVO importTopicCourses(HttpServletRequest request, List<CoursesTopicImportExcelVO> coursesTopicImportExcelVOS) throws InterruptedException, IOException {
        List<CoursesTopicImportResultExcelVO> list = CoursesConvert.INSTANCE.convertList05(coursesTopicImportExcelVOS);
        return this.importCourses(request, list, CoursesTypeEnum.TOPIC_COURSE.getType());
    }


    /**
     * 从Excel导入的课程列表中获取所有教师的名字
     *
     * @param list Excel导入的课程列表
     * @return 教师名字列表
     */
    private List<String> getTeacherNameList(List<CoursesTopicImportResultExcelVO> list) {
        List<String> teacherNameList = new ArrayList<>();
        for (CoursesTopicImportResultExcelVO vo : list) {
            String teacherNames = vo.getTeacherNameList();
            if (StrUtil.isNotBlank(teacherNames)) {
                teacherNames = teacherNames.replace(",", "，");
                Arrays.asList(teacherNames.split("，")).forEach(name->{
                    teacherNameList.add(name.trim());
                });
            }
        }
        return teacherNameList;
    }

    /**
     * 将教师姓名逗号分隔字符串拆分为去重后的集合
     *
     * @param teacherNames 多教师姓名字符串
     * @return 去重后的教师姓名集合
     */
    private Set<String> splitTeacherNames(String teacherNames) {
        if (StrUtil.isBlank(teacherNames)) {
            return new HashSet<>();
        }
        teacherNames = teacherNames.replace(",", "，");
        Set<String> teacherNameSet = new HashSet<>();
        if (StrUtil.isNotBlank(teacherNames)) {
            Arrays.asList(teacherNames.split("，")).forEach(name->{
                teacherNameSet.add(name.trim());
            });
        }
        return teacherNameSet;
    }

    // 辅助方法，用于检查列表中是否存在内容相同的集合
    private static boolean containsSameSet(List<Set<String>> list, Set<String> set) {
        for (Set<String> existingSet : list) {
            if (existingSet.equals(set)) {
                return true;
            }
        }
        return false;
    }



    /**
     * 导入课程
     *
     * @param request     请求
     * @param list        导入数据
     * @param coursesType 课程类型
     * @return 导入结果
     */
    private ExcelImportResultRespVO importCourses(HttpServletRequest request, List<CoursesTopicImportResultExcelVO> list, Integer coursesType) throws InterruptedException, IOException {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }
        // 导入数据为空
        if (list.isEmpty()) {
            throw exception(COURSE_IMPORT_EMPTY);
        }

        // excel中的待绑定的教师名称列表
        List<String> teacherNameList = getTeacherNameList(list);
        // 获取导入信息中的授课教师信息
        List<TeacherInformationDO> teacherInformationDOS = teacherInformationMapper.selectListByNames(teacherNameList);


        List<String> names = list.stream().map(CoursesTopicImportResultExcelVO::getName).collect(Collectors.toList());
        // 根据excel中课程名称查询已存在的课程信息
        List<CoursesDO> coursesRespVOList = coursesMapper.selectListByNames(names, coursesType);
        // 校验课程是否重复 课程名称-> 课程信息列表
        Map<String, List<CoursesDO>> existsCoursesName2CourseList = coursesRespVOList.stream()
                .collect(Collectors.groupingBy(CoursesDO::getName));


        // 查询excel中已存在课程名称的授课教师信息
        List<Long> existCourseIds = coursesRespVOList.stream().map(CoursesDO::getId).collect(Collectors.toList());
        List<TeacherCourseInformationDO> existsTeacherCourseInformationDOS = teacherCourseInformationMapper.selectListByCoursesIds(existCourseIds);

        // 获取教学形式、教学方式、课程分类所有字典选项
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.EDUCATE_FORM.getType(), CoursesDictEnum.TEACHING_METHOD.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.THEME.getType())).getCheckedData();
        List<DictDataRespDTO> themeDictData = dictDataList.stream()
                .filter(dictDataRespDTO -> dictDataRespDTO.getDictType().equals(CoursesDictEnum.getThemeTypeByCoursesType(coursesType)))
                .collect(Collectors.toList());
        List<DictDataRespDTO> educateFormData = dictDataList.stream()
                .filter(dictDataRespDTO -> dictDataRespDTO.getDictType().equals(CoursesDictEnum.EDUCATE_FORM.getType()))
                .collect(Collectors.toList());
        List<DictDataRespDTO> teachingMethodData = dictDataList.stream()
                .filter(dictDataRespDTO -> dictDataRespDTO.getDictType().equals(CoursesDictEnum.TEACHING_METHOD.getType()))
                .collect(Collectors.toList());
        // 获取所有课程分类的id->完整路径Map
        Map<String, Long> fullPathMap2themeId = TreeDataUtil.generateFullPathToIdMap(packageDictDataAsTreeNodeDTOList(themeDictData));
        // 获取所有教学形式的id->完整路径Map
        Map<String, Long> fullPathMap2educateFormId = TreeDataUtil.generateFullPathToIdMap(packageDictDataAsTreeNodeDTOList(educateFormData));
        // 获取所有教学方式的id->完整路径Map
        Map<String, Long> fullPathMap2teachingMethodId = TreeDataUtil.generateFullPathToIdMap(packageDictDataAsTreeNodeDTOList(teachingMethodData));
        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request))
                .getCheckedData();
        // 获取所有管理部门的id->完整路径Map
        Map<String, Long> fullPathMap2deptId = TreeDataUtil.generateFullPathToIdMap(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单:{}", fullPathMap2deptId);

        if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(coursesType)) {
            // 专题课校验（课程名、授课教师）是否重复
            Map<String, List<Set<String>>> existName2TeacherSetMap = new HashMap<>();
            list.forEach(importedExcelVO -> {
                if (StrUtil.isNotBlank(importedExcelVO.getName())) {
                    List<Set<String>> teacherNameSets = existName2TeacherSetMap.get(importedExcelVO.getName());

                    // 获取该行授课教师姓名集合
                    Set<String> teacherNames = splitTeacherNames(importedExcelVO.getTeacherNameList());

                    // 如果不存在同名课程，则直接添加到existName2TeacherSetMap中
                    if (Objects.isNull(teacherNameSets)) {
                        teacherNameSets = new ArrayList<>(); // 创建一个可修改的列表
                        teacherNameSets.add(teacherNames);    // 添加教师姓名集合
                        existName2TeacherSetMap.put(importedExcelVO.getName(), teacherNameSets);
                        return;
                    }

                    if (containsSameSet(teacherNameSets, teacherNames)) {
                        importedExcelVO.setError("表内相同课程重复导入");
                    } else {
                        teacherNameSets.add(teacherNames);
                    }
                }
            });
        }else if (CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(coursesType)) {
            // 选修课仅校验课程名是否重复
            Set<String> existNameSet = new HashSet<>();
            // 表内名称重复导入过滤
            list.forEach(importedExcelVO -> {
                if (StrUtil.isNotBlank(importedExcelVO.getName())) {
                    if (existNameSet.contains(importedExcelVO.getName())) {
                        importedExcelVO.setError("表内相同课程重复导入");
                    } else {
                        existNameSet.add(importedExcelVO.getName());
                    }
                }
            });
        }


        List<CoursesDO> addCoursesList = new ArrayList<>();
        List<CoursesDO> updateCoursesList = new ArrayList<>();

        // 需要删除的授课教师信息 关联关系id
        List<Long> toBeDeleteTeacherCourseIds= new ArrayList<>();
        // 需要新增的授课教师信息 因为一开始新增的course没有id,需要插入后才有
        // IdentityHashMap 存对象是不会使用对象里面的hashCode和equals方法来判断是否重复，而是通过对象的内存地址来判断
        Map<CoursesDO,Set<Long>> toBeAddCourse2TeacherIdsMap = new IdentityHashMap<>();

        // 错误列表
        List<CoursesTopicImportResultExcelVO> errorImportList = new ArrayList<>();

        for (CoursesTopicImportResultExcelVO vo : list) {
            if (Objects.isNull(vo.getError())) {
                vo.setError("");
            }

            Long themeId = null;
            Long educateFormId = null;
            Long teachingMethodId = null;
            Long managementDeptId = null;

            // 校验课程名称
            if (StringUtils.isBlank(vo.getName())) {
                if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(coursesType)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_TOPIC_NAME_REQUIRED.getMsg());
                } else {
                    vo.setError(vo.getError() + COURSES_IMPORT_OPTIONAL_NAME_REQUIRED.getMsg());
                }
            } else {
                vo.setName(vo.getName().trim());
                // 限制50个字符
                if (vo.getName().length() > 50) {
                    vo.setError(vo.getError() + CoursesTypeEnum.getDescByType(coursesType) + COURSES_IMPORT_NAME_LENGTH_LIMIT.getMsg());
                }
            }

            // 校验课程分类
            if (StringUtils.isBlank(vo.getTheme())) {
                if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(coursesType)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_TOPIC_THEME_REQUIRED.getMsg());
                } else {
                    vo.setError(vo.getError() + COURSES_IMPORT_OPTIONAL_THEME_REQUIRED.getMsg());
                }
            } else {
                themeId = fullPathMap2themeId.get(vo.getTheme());
                if (Objects.isNull(themeId)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_THEME_NOT_EXISTS.getMsg());
                }
            }

            // 校验教学形式
            if (StringUtils.isBlank(vo.getEducateForm())) {
                vo.setError(vo.getError() + COURSES_IMPORT_EDUCATE_FORM_REQUIRED.getMsg());
            } else {
                educateFormId = fullPathMap2educateFormId.get(vo.getEducateForm());
                if (Objects.isNull(educateFormId)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_EDUCATE_FORM_NOT_EXISTS.getMsg());
                }
            }

            // 校验教学方式
            if (StringUtils.isBlank(vo.getTeachingMethod())) {
                vo.setError(vo.getError() + COURSES_IMPORT_TEACHING_METHOD_REQUIRED.getMsg());
            } else {
                teachingMethodId = fullPathMap2teachingMethodId.get(vo.getTeachingMethod());
                if (Objects.isNull(teachingMethodId)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_TEACHING_METHOD_NOT_EXISTS.getMsg());
                }
            }

            // 校验管理部门
            if (StringUtils.isNotBlank(vo.getManagementDept())) {
                managementDeptId = fullPathMap2deptId.get(vo.getManagementDept());
                if (Objects.isNull(managementDeptId)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_MANAGEMENT_DEPT_NOT_EXISTS.getMsg());
                }
            }

            Set<String> splitTeacherNames = splitTeacherNames(vo.getTeacherNameList());
            Set<Long> teacherIds = new HashSet<>();
            // 校验授课教师
            if (StringUtils.isNotBlank(vo.getTeacherNameList())){
                List<String> teacherNames = new ArrayList<>(splitTeacherNames);
                // 不存在的教师
                List<String> notExistTeacherNames = new ArrayList<>();
                // 重名的教师
                List<String> duplicateTeacherNames = new ArrayList<>();

                for (String teacherName : teacherNames) {
                    List<TeacherInformationDO> informationDOS = teacherInformationDOS.stream().filter(o -> teacherName.equals(o.getName())).collect(Collectors.toList());
                    // 该教师姓名不存在
                    if (informationDOS.isEmpty()) {
                        notExistTeacherNames.add(teacherName);
                    } else if (informationDOS.size() == 1) {
                        // 记录id
                        teacherIds.add(informationDOS.get(0).getId());
                    } else {
                        // 该名称老师有多个，不允许导入
                        duplicateTeacherNames.add(teacherName);
                    }
                }

                if (!notExistTeacherNames.isEmpty()) {
                    vo.setError(vo.getError() + " " + StringUtils.join(notExistTeacherNames, '、')
                            + COURSES_IMPORT_TEACHER_NOT_EXISTS.getMsg());
                }

                if (!duplicateTeacherNames.isEmpty()) {
                    vo.setError(vo.getError() + " " + StringUtils.join(duplicateTeacherNames, '、')
                            + COURSES_IMPORT_TEACHER_DUPLICATE.getMsg());
                }
            }



            if (StringUtils.isBlank(vo.getError())) {
                CoursesDO coursesDO = new CoursesDO();;
                // 覆盖
                if (existsCoursesName2CourseList.containsKey(vo.getName())) {
                    if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(coursesType)){
                        // 专题课程需要校验（名称+授课教师）
                        List<CoursesDO> coursesDOS = existsCoursesName2CourseList.get(vo.getName());
                        for (CoursesDO subCoursesDO : coursesDOS) {
                            // 获取同名课程授课教师
                            Set<Long> existsTeacherIds = existsTeacherCourseInformationDOS.stream().filter(o -> Objects.equals(subCoursesDO.getId(), o.getCoursesId()))
                                    .map(TeacherCourseInformationDO::getTeacherId).collect(Collectors.toSet());
                            // 授课教师相同
                            if (existsTeacherIds.equals(teacherIds)) {
                                coursesDO = subCoursesDO;
                                break;
                            }
                        }
                    }else {
                        // 选修课只需要校验名称重复即可，无需校验其他字段
                        List<CoursesDO> coursesDOS = existsCoursesName2CourseList.get(vo.getName());
                        coursesDO = coursesDOS.get(0);
                    }

                }

                coursesDO.setName(vo.getName());
                coursesDO.setThemeId(themeId);
                coursesDO.setEducateFormId(educateFormId);
                coursesDO.setTeachingMethodId(teachingMethodId);
                // 只有新增或者管理部门字段填了，才更新 （当覆盖时且管理部门未填，则不更新）
                if (Objects.isNull(coursesDO.getId()) || Objects.nonNull(managementDeptId)) {
                    coursesDO.setManagementDeptId(managementDeptId);
                }
                coursesDO.setCoursesType(coursesType);

                if (Objects.isNull(coursesDO.getId())) {
                    // 新增
                    addCoursesList.add(coursesDO);
                    // 新增授课教师关联关系
                    toBeAddCourse2TeacherIdsMap.put(coursesDO, teacherIds);
                } else {
                    // 覆盖下修改
                    updateCoursesList.add(coursesDO);

                    // 更新授课教师关联关系
                    long doId = coursesDO.getId();

                    // 获取该课程授课教师关联关系
                    List<TeacherCourseInformationDO> teacherCourseInformationDOS = existsTeacherCourseInformationDOS.stream()
                            .filter(o -> doId == o.getCoursesId()).collect(Collectors.toList());

                    Set<Long> existTeacherIds = teacherCourseInformationDOS.stream()
                            .map(TeacherCourseInformationDO::getTeacherId).collect(Collectors.toSet());

                    // 教师id->关联关系id
                    Map<Long,Long> teacherId2InformationIdsMap = teacherCourseInformationDOS.stream()
                            .collect(Collectors.toMap(TeacherCourseInformationDO::getTeacherId, TeacherCourseInformationDO::getId));

                    // 获得需要删除的教师-课程关联关系ID
                    Set<Long> toBeDeletedIds = existTeacherIds.stream()
                            .filter(id -> !teacherIds.contains(id))
                            .map(teacherId2InformationIdsMap::get)
                            .collect(Collectors.toSet());
                    if (!toBeDeletedIds.isEmpty()) {
                        toBeDeleteTeacherCourseIds.addAll(toBeDeletedIds);
                    }

                    // 获得需要新增的教师 ID
                    Set<Long> toBeInsertedTeacherIds = teacherIds.stream()
                            .filter(id -> !existTeacherIds.contains(id))
                            .collect(Collectors.toSet());
                    toBeAddCourse2TeacherIdsMap.put(coursesDO, toBeInsertedTeacherIds);
                }
            } else {
                // 错误列表新增
                errorImportList.add(vo);
            }

        }

        if (!addCoursesList.isEmpty()) {
            coursesMapper.insertBatch(addCoursesList);
        }
        // 批量更新
        if (!updateCoursesList.isEmpty()) {
            coursesMapper.updateBatch(updateCoursesList);
        }

        // 删除教师课程关联关系
        if (!toBeDeleteTeacherCourseIds.isEmpty()) {
            teacherCourseInformationMapper.deleteBatchIds(toBeDeleteTeacherCourseIds);
        }

        // 新增教师课程关联关系
        List<TeacherCourseInformationDO> addList = new ArrayList<>();
        toBeAddCourse2TeacherIdsMap.forEach((coursesDO, teacherIds) -> {
            if (Objects.isNull(teacherIds)){
                return;
            }
            teacherIds.forEach(id -> {
                TeacherCourseInformationDO teacherCourseInformationDO = new TeacherCourseInformationDO();
                teacherCourseInformationDO.setCoursesId(coursesDO.getId());
                teacherCourseInformationDO.setTeacherId(id);
                addList.add(teacherCourseInformationDO);
            });
        });

        if (!addList.isEmpty()) {
            teacherCourseInformationMapper.insertBatch(addList);
        }

        // 导入失败数量
        int failNumber = errorImportList.size();
        // 导入成功数量
        int successNumber = list.size() - failNumber;
        log.info("课程导入完成，成功：{}，失败：{}", successNumber, failNumber);
        // 错误列表上传url
        String errorExcelUrl = null;
        // 导出错误列表
        if (!errorImportList.isEmpty()) {
            if (CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(coursesType)) {
                List<CoursesOptionalImportResultExcelVO> errorImportList2 = CoursesConvert.INSTANCE.convertList06(errorImportList);
                errorExcelUrl = UploadExcel(errorImportList2, "导入失败明细.xlsx", "导入失败列表");
            } else {
                errorExcelUrl = UploadExcel(errorImportList, "导入失败明细.xlsx", "导入失败列表");
            }
        }
        return new ExcelImportResultRespVO(successNumber, failNumber, errorExcelUrl, "导入失败明细.xlsx");

    }

    /**
     * 导入教学活动
     *
     * @param request 请求
     * @param list    导入数据
     * @return 导入结果
     */
    @Override
    public ExcelImportResultRespVO importActivityCourses(HttpServletRequest request, List<CoursesActivityImportResultExcelVO> list) {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }
        // 导入数据为空
        if (list.isEmpty()) {
            throw exception(COURSE_IMPORT_EMPTY);
        }

        // 根据excel中课程名称获取课程信息
        List<String> names = list.stream().map(CoursesActivityImportResultExcelVO::getName).collect(Collectors.toList());
        List<CoursesDO> coursesRespVOList = coursesMapper.selectListByNames(names, CoursesTypeEnum.TEACHING_ACTIVITY.getType());
        Map<String, CoursesDO> existsCoursesName2Course = coursesRespVOList.stream()
                .collect(Collectors.toMap(CoursesDO::getName, course -> course, (existing, replacement) -> existing));

        // 获取所有教学形式字典
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(Collections.singletonList(CoursesDictEnum.EDU_ACTIVITY.getType())).getCheckedData();
        // label -> value
        Map<String, String> activityTypeLabelToValueMap = dictDataList.stream().collect(Collectors.toMap(DictDataRespDTO::getLabel, DictDataRespDTO::getValue, (existing, replacement) -> existing));
        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request))
                .getCheckedData();
        // 获取所有管理部门的id->完整路径Map
        Map<String, Long> fullPathMap2deptId = TreeDataUtil.generateFullPathToIdMap(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单:{}", fullPathMap2deptId);
        Set<String> existNameSet = new HashSet<>();
        // 表内名称重复导入过滤
        list.forEach(importedExcelVO -> {
            if (StrUtil.isNotBlank(importedExcelVO.getName()) && existNameSet.contains(importedExcelVO.getName())) {
                importedExcelVO.setError("表内相同课程重复导入");
            } else {
                existNameSet.add(importedExcelVO.getName());
            }
        });

        List<CoursesDO> addCoursesList = new ArrayList<>();
        List<CoursesDO> updateCoursesList = new ArrayList<>();

        // 错误列表
        List<CoursesActivityImportResultExcelVO> errorImportList = new ArrayList<>();

        for (CoursesActivityImportResultExcelVO vo : list){
            if (Objects.isNull(vo.getError())) {
                vo.setError("");
            }

            // 管理部门ID
            Long managementDeptId = null;
            // 活动类型
            Long typeByDesc = null;
            // 开发日期
            LocalDate date = null;

            // 校验课程名称
            if (StringUtils.isBlank(vo.getName())) {
                    vo.setError(vo.getError() + COURSES_IMPORT_ACTIVITY_NAME_REQUIRED.getMsg());
            } else {
                vo.setName(vo.getName().trim());
                // 限制50个字符
                if (vo.getName().length() > 50) {
                    vo.setError(vo.getError() + " " + CoursesTypeEnum.TEACHING_ACTIVITY.getDesc() +
                            COURSES_IMPORT_NAME_LENGTH_LIMIT.getMsg());
                }
            }

            // 开发时间
            if (StringUtils.isNotBlank(vo.getDate())) {
                try {
                    date = DateUtils.parseLocalDate(vo.getDate(), FORMAT_YEAR_MONTH_DAY);
                } catch (Exception e) {
                    vo.setError(vo.getError() + COURSES_IMPORT_DATE_FORMAT_ERROR.getMsg());
                }
            }

            // 校验活动类型
            if (StringUtils.isNotBlank(vo.getActivityTypeName())) {
                String activityTypeValue = activityTypeLabelToValueMap.getOrDefault(vo.getActivityTypeName().trim(), null);
                if (StringUtils.isBlank(activityTypeValue)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_ACTIVITY_TYPE_NOT_EXISTS.getMsg());
                } else {
                    typeByDesc = Long.valueOf(activityTypeValue);
                }
            }

            // 校验管理部门
            if (StringUtils.isNotBlank(vo.getManagementDept())) {
                managementDeptId = fullPathMap2deptId.get(vo.getManagementDept());
                if (Objects.isNull(managementDeptId)) {
                    vo.setError(vo.getError() + COURSES_IMPORT_MANAGEMENT_DEPT_NOT_EXISTS.getMsg());
                }
            }

            if (StringUtils.isBlank(vo.getError())) {
                CoursesDO coursesDO;
                // 覆盖
                if (existsCoursesName2Course.containsKey(vo.getName())) {
                    coursesDO = existsCoursesName2Course.get(vo.getName());
                } else {
                    // 新增
                    coursesDO = new CoursesDO();
                }

                coursesDO.setName(vo.getName());
                // 只有新增或者管理部门字段填了，才更新 （当覆盖时且管理部门未填，则不更新）
                if (Objects.nonNull(managementDeptId)) {
                    coursesDO.setManagementDeptId(managementDeptId);
                }

                // 只有新增的时候或者活动类型字段填了，才更新 （当覆盖时且活动类型未填，则不更新）
                if (Objects.nonNull(typeByDesc)) {
                    coursesDO.setActivityType(typeByDesc);
                }

                if (Objects.nonNull(date)) {
                    coursesDO.setDate(date);
                }

                coursesDO.setCoursesType(CoursesTypeEnum.TEACHING_ACTIVITY.getType());

                if (Objects.isNull(coursesDO.getId())) {
                    // 新增
                    addCoursesList.add(coursesDO);
                } else {
                    // 修改
                    updateCoursesList.add(coursesDO);
                }
            } else {
                // 错误列表新增
                errorImportList.add(vo);
            }
        }

        if (!addCoursesList.isEmpty()) {
            coursesMapper.insertBatch(addCoursesList);
        }
        // 批量更新
        if (!updateCoursesList.isEmpty()) {
            coursesMapper.updateBatch(updateCoursesList);
        }

        // 导入失败数量
        int failNumber = errorImportList.size();
        // 导入成功数量
        int successNumber = list.size() - failNumber;
        log.info("课程导入完成，成功：{}，失败：{}", successNumber, failNumber);
        // 错误列表上传url
        String errorExcelUrl = null;
        // 导出错误列表
        if (!errorImportList.isEmpty()) {
            errorExcelUrl = UploadExcel(errorImportList, "导入失败明细.xlsx", "导入失败列表");
        }

        return new ExcelImportResultRespVO(successNumber, failNumber, errorExcelUrl, "导入失败明细.xlsx");
    }

    @Override
    public CoursesRespVO getCourses(Long id) {
        CoursesDO coursesDO = coursesMapper.selectById(id);
        if (Objects.isNull(coursesDO)) {
            throw exception(COURSES_NOT_EXISTS);
        }
        // 获取该课程的授课教师
        List<Long> teacherIdList = teacherCourseInformationMapper.selectListByCoursesId(id)
                .stream().map(TeacherCourseInformationDO::getTeacherId).collect(Collectors.toList());
        CoursesRespVO coursesRespVO = CoursesConvert.INSTANCE.convert(coursesDO);
        coursesRespVO.setTeacherIdList(teacherIdList);
        return coursesRespVO;
    }

    @Override
    public List<CoursesDO> getCoursesList(Collection<Long> ids) {
        return coursesMapper.selectBatchIds(ids);
    }

    /**
     * 获取导出数据
     *
     * @param pageReqVO 查询条件
     * @return 导出数据
     */
    private List<CoursesRespVO> getExportDataListByIdsOrReqVO(CoursesPageReqVO pageReqVO, HttpServletRequest request) {
        List<CoursesRespVO> coursesRespVOList;
        // 如果是勾选导出
        if (Objects.nonNull(pageReqVO.getIds()) && !pageReqVO.getIds().isEmpty()) {
            coursesRespVOList = coursesMapper.selectListByIds(pageReqVO.getIds());
            // 填充课程分类和教学形式字典、管理部门
            packageDictData(coursesRespVOList, request);
        }
        // 如果是条件筛选
        else {
            pageReqVO.setPageNo(1);
            pageReqVO.setPageSize(Integer.MAX_VALUE);
            coursesRespVOList = getCoursesPage(pageReqVO, request).getList();
        }
        return coursesRespVOList;
    }

    /**
     * 组装课程信息 来源于字典中同步的业中课程分类、教学形式、管理部门
     *
     * @param coursesRespVOList 课程信息
     */
    private void packageDictData(List<CoursesRespVO> coursesRespVOList, HttpServletRequest request) {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }
        // 组装管理部门
        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request))
                .getCheckedData();
        // 获取所有管理部门的id->完整路径Map
        Map<Long, String> fullPathMap2deptId = TreeDataUtil.generateNodeIdToFullPathMap(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单:{}", fullPathMap2deptId);
        // 获取课程分类 教学形式 教学方式 业中同步过来的字典数据
        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.THEME.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.EDUCATE_FORM.getType(), CoursesDictEnum.TEACHING_METHOD.getType())).getCheckedData();
        // 字典数据列表转树节点用于计算
        List<TreeNodeDTO> treeNodeDTOList = packageDictDataAsTreeNodeDTOList(dictData);
        // 课程分类、教学形式 字典id->完整路径表示ID
        Map<Long, String> map = TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);
        coursesRespVOList.forEach(coursesRespVO -> {
            if (Objects.nonNull(coursesRespVO.getThemeId())) {
                coursesRespVO.setTheme(map.get(coursesRespVO.getThemeId())); // 课程分类名
            }
            if (Objects.nonNull(coursesRespVO.getEducateFormId())) {
                coursesRespVO.setEducateForm(map.get(coursesRespVO.getEducateFormId())); // 教学形式
            }
            if (Objects.nonNull(coursesRespVO.getTeachingMethodId())) {
                coursesRespVO.setTeachingMethod(map.get(coursesRespVO.getTeachingMethodId())); // 教学方式
            }
            if (Objects.nonNull(coursesRespVO.getManagementDeptId())) {
                coursesRespVO.setManagementDept(fullPathMap2deptId.get(coursesRespVO.getManagementDeptId()));
            }
        });
    }

    private CoursesDO validateCoursesExists(Long id) {
        CoursesDO coursesDO = coursesMapper.selectById(id);
        if (Objects.isNull(coursesDO)) {
            throw exception(COURSES_NOT_EXISTS);
        }
        return coursesDO;
    }

    /**
     * 从请求头中获取token
     *
     * @param request 请求对象
     * @return token
     */
    private String getTokenFromRequestHead(HttpServletRequest request) {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        // 如果token以"Bearer "开头，则提取实际的token值
        if (token != null && token.startsWith("Bearer ")) {
            // 去除"Bearer "部分
            token = token.substring(7);
        }
        log.info("获得请求头Authorization:{}", token);
        return token;
    }

    private Long getTenantId() {
        LoginUser loginUser = getLoginUser();
        if (Objects.nonNull(loginUser)) {
            return loginUser.getTenantId();
        }
        return null;
    }

    /**
     * 将字典数据列表转换为树节点列表
     *
     * @param list 字典数据列表
     * @return 树节点列表
     */
    public List<TreeNodeDTO> packageDictDataAsTreeNodeDTOList(List<DictDataRespDTO> list) {
        return list.stream().map(data -> new TreeNodeDTO(data.getId(), data.getParentId(), data.getLabel())).collect(Collectors.toList());
    }

    private List<TreeNodeDTO> packageDeptDataAsTreeNodeDTOList(List<DeptSimpleRespDTO> list) {
        return list.stream().map(data -> new TreeNodeDTO(data.getId(), data.getParentId(), data.getName())).collect(Collectors.toList());
    }

}
