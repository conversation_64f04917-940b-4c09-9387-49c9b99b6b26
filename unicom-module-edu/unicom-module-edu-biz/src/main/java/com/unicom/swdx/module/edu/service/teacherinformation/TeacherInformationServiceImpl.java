package com.unicom.swdx.module.edu.service.teacherinformation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.crypt.sm2.SM2Utils;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.common.util.desensitize.DesensitizeUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseClassTimeTeacherReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.MyEvaluationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.MyEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.dto.TeacherInformationClassCourseDTO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.dto.TeacherTeachingTimeInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.convert.teacherinformation.TeacherInformationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformatiomnDoConvert;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electiverelease.ElectiveReleaseMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleaseclasses.ElectiveReleaseClassesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses.ElectiveReleaseCoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
import com.unicom.swdx.module.edu.dal.mysql.teachercourseinformation.TeacherCourseInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.enums.courses.CoursesDictEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.plan.PlanStatusEnum;
import com.unicom.swdx.module.edu.enums.teacherinformation.TeacherInformationDictEnum;
import com.unicom.swdx.module.edu.enums.teacherinformation.TeacherInformationSourceEnum;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.plan.PlanServiceImpl;
import com.unicom.swdx.module.edu.utils.IdCard.CheckIdCard;
import com.unicom.swdx.module.edu.utils.excel.ExcelImportResultRespVO;
import com.unicom.swdx.module.edu.utils.tree.TreeDataUtil;
import com.unicom.swdx.module.edu.utils.tree.dto.TreeNodeDTO;
import com.unicom.swdx.module.edu.utils.validate.ValidateMatchUtils;
import com.unicom.swdx.module.system.api.businesscenter.BusinessCenterApi;
import com.unicom.swdx.module.system.api.businesscenter.dto.DeptSimpleRespDTO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.CreateUserBatchTenantDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.parameters.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;
import static com.unicom.swdx.module.edu.utils.upload.UploadFileUtil.UploadExcel;

@Slf4j
@Service
public class TeacherInformationServiceImpl extends ServiceImpl<TeacherInformationMapper, TeacherInformationDO> implements TeacherInformationService {

    @Resource
    private TenantApi tenantApi;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;


    @Resource
    private TeacherCourseInformationMapper teacherCourseInformationMapper;

    @Resource
    private CoursesMapper coursesMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private BusinessCenterApi businessCenterApi;

    @Resource
    AdminUserApi adminUserApi;

    @Resource
    private PlanServiceImpl planService;

    @Resource
    private TeacherDeptService teacherDeptService;

    // 校外教师默认部门id
    private final String OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_ID = "-2";
    // 校外教师默认部门名称
    private final String OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_NAME = "校外兼职教师";

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    public static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }

    public static String desensitizeDecrypt(String content) {
        return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
    }

    @Resource
    private ElectiveReleaseCoursesMapper electiveReleaseCoursesMapper;

    @Resource
    private ElectiveReleaseMapper electiveReleaseMapper;

    @Resource
    private EvaluationResponseMapper evaluationResponseMapper;

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private ElectiveReleaseClassesMapper electiveReleaseClassesMapper;

    /**
     * 案例式教学-字典标签
     */
    private static final String  CASE_BASED_TEACHING_LABEL = "案例式";

    /**
     * 专题研究式教学-字典标签
     */
    private static final String  RESEARCH_TEACHING_LABEL = "专题研究式教学";

    @Override
    public Long createTeacherInformation(TeacherInformationCreateReqVO createReqVO) {
        Long tenantId = getTenantId();

        TeacherInformationDO teacher = TeacherInformationConvert.INSTANCE.convert(createReqVO);

        // 未填部门默认设置为校外教师
        if (StringUtils.isBlank(teacher.getDeptIds())) {
            teacher.setDeptIds(OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_ID);
            teacher.setDeptNames(OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_NAME);
        }

        teacher.setTenantId(tenantId);

        String contactInformation = teacher.getContactInformation();
        if (contactInformation != null && contactInformation != "") {
            String decryptedContactInformation = desensitizeDecrypt((contactInformation));
            teacher.setContactInformation(decryptedContactInformation);
        }
        String idNumber = teacher.getIdNumber();
        if (idNumber != null && idNumber != "") {
            String decryptedIdNumber = desensitizeDecrypt((idNumber));
            teacher.setIdNumber(decryptedIdNumber);
        }
        String professionalTitle = teacher.getProfessionalTitle();
        if (professionalTitle != null && professionalTitle != "") {
            String decryptedProfessionalTitle = desensitizeDecrypt((professionalTitle));
            teacher.setProfessionalTitle(decryptedProfessionalTitle);
        }

        Long teacherId = validateTeacherExistsByIdNumber(teacher);
        // 校验是否存在
        if (teacherId == -1L) {
            // 插入师资信息
            teacherInformationMapper.insert(teacher);
            // 关联课程
            if (Objects.nonNull(createReqVO.getCoursesIds()) && !createReqVO.getCoursesIds().isEmpty()) {
                // 插入关联关系
                ArrayList<TeacherCourseInformationDO> insertTeacherCourseInformation = new ArrayList<>();
                for (Long coursesId : createReqVO.getCoursesIds()) {
                    insertTeacherCourseInformation.add(TeacherCourseInformationDO.builder()
                            .coursesId(coursesId)
                            .teacherId(teacher.getId())
                            .build());
                }
                teacherCourseInformationMapper.insertBatch(insertTeacherCourseInformation);
            }
        } else {
            teacher.setId(teacherId);
            TeacherInformationUpdateReqVO updateReqVO = TeacherInformationConvert.INSTANCE.convertToUpdateReqVO(teacher);
            updateTeacherInformation(updateReqVO, false);
        }
        return teacher.getId();
    }

    private void validateCourseArranged(List<Long> courseIds, Long teacherId) {
        List<TeacherLectureTimeRespVO> lectureInfo = classCourseMapper.getLectureInfoByTeacherId(teacherId);
        // 已经安排授课的课程id
        List<Long> arrangedCourseIds = lectureInfo.stream().map(TeacherLectureTimeRespVO::getCourseId).distinct().collect(Collectors.toList());

        if(arrangedCourseIds != null && Objects.nonNull(courseIds) && !courseIds.isEmpty() && !courseIds.containsAll(arrangedCourseIds)) {
            throw exception(COURSE_ARRANGED);
        }
    }

    private Long validateTeacherExistsByIdNumber(TeacherInformationDO teacher) {
        LambdaQueryWrapperX<TeacherInformationDO> wrapperX = new LambdaQueryWrapperX<>();
        // 校内教师查询hrId
        if(teacher.getSource() == 0) {
            wrapperX.eq(TeacherInformationDO::getHrId, teacher.getHrId());

        } else { //校外教师查身份证
            if(teacher.getIdNumber() != null && !teacher.getIdNumber().isEmpty()) {
                wrapperX.eq(TeacherInformationDO::getIdNumber, teacher.getIdNumber())
                        .eq(TeacherInformationDO::getSource, 1);
            } else if(teacher.getContactInformation() != null && !teacher.getContactInformation().isEmpty()){
                wrapperX.eq(TeacherInformationDO::getContactInformation, teacher.getContactInformation())
                        .eq(TeacherInformationDO::getSource, 1);
            } else {
                return -1L;
            }

        }
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectOne(wrapperX);
        if(teacherInformationDO != null){
            return teacherInformationDO.getId();
        } else {
            return -1L;
        }
    }

    @Override
    public void updateTeacherInformation(TeacherInformationUpdateReqVO updateReqVO, Boolean isEncrypted) {
        validateTeacherExists(updateReqVO.getId());
        validateCourseArranged(updateReqVO.getCoursesIds(), updateReqVO.getId());
        TeacherInformationDO teacher = TeacherInformationConvert.INSTANCE.convert(updateReqVO);
        if (isEncrypted) {
            String contactInformation = teacher.getContactInformation();
            if (contactInformation != null && contactInformation != "") {
                String decryptedContactInformation = desensitizeDecrypt((contactInformation));
                teacher.setContactInformation(decryptedContactInformation);
            }
            String idNumber = teacher.getIdNumber();
            if (idNumber != null && idNumber != "") {
                String decryptedIdNumber = desensitizeDecrypt((idNumber));
                teacher.setIdNumber(decryptedIdNumber);
            }
            String professionalTitle = teacher.getProfessionalTitle();
            if (professionalTitle != null && professionalTitle != "") {
                String decryptedProfessionalTitle = desensitizeDecrypt((professionalTitle));
                teacher.setProfessionalTitle(decryptedProfessionalTitle);
            }
        }
        // 关联对应课程
        if (Objects.nonNull(updateReqVO.getCoursesIds()) && !updateReqVO.getCoursesIds().isEmpty()) {
            // 对课程id去重
            Set<Long> processCoursesIds = updateReqVO.getCoursesIds().stream().collect(Collectors.toSet());
            // 查询当前教师已有的课程关联关系
            List<TeacherCourseInformationDO> informationList = teacherCourseInformationMapper.selectListByTeacherId(updateReqVO.getId());
            // 课程ID -> 关联关系ID列表
            Map<Long, Long> courseId2InformationIdsMap = informationList.stream()
                    .collect(Collectors.toMap(TeacherCourseInformationDO::getCoursesId, TeacherCourseInformationDO::getId));
            // 已有的关联课程ID列表
            Set<Long> existCourseIds = informationList.stream().map(TeacherCourseInformationDO::getCoursesId).collect(Collectors.toSet());
            // 获得需要删除的课程 ID
            Set<Long> toBeDeletedIds = existCourseIds.stream()
                    .filter(id -> !processCoursesIds.contains(id))
                    .map(courseId2InformationIdsMap::get)
                    .collect(Collectors.toSet());
            if(toBeDeletedIds != null && !toBeDeletedIds.isEmpty()) {
                teacherCourseInformationMapper.deleteBatchIds(toBeDeletedIds);
            }
            // 获得需要新增的课程 ID
            Set<Long> toBeInsertedCourseIds = processCoursesIds.stream()
                    .filter(id -> !existCourseIds.contains(id))
                    .collect(Collectors.toSet());

            List<TeacherCourseInformationDO> toBeInsert = new ArrayList<>();
            toBeInsertedCourseIds.forEach(coursesId -> toBeInsert.add(TeacherCourseInformationDO.builder()
                    .teacherId(updateReqVO.getId())
                    .coursesId(coursesId)
                    .build()));
            teacherCourseInformationMapper.insertBatch(toBeInsert);
        }
        // 未填部门默认设置为校外教师
        if (StringUtils.isBlank(teacher.getDeptIds())) {
            teacher.setDeptIds(OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_ID);
            teacher.setDeptNames(OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_NAME);
        }
        teacherInformationMapper.updateById(teacher);
    }

    @Override
    public void deleteTeacherInformation(Long id) {
        // 校验存在
        validateTeacherExists(id);

        //检查该老师是否已经排课
        List<ClassCourseDO> courseList = classCourseMapper.getClassCourseByTeacherId(id);
        if (CollUtil.isNotEmpty(courseList)){
            throw exception(COURSE_ARRANGED);
        }

        // 删除关联课程信息
        teacherCourseInformationMapper.deleteByTeacherId(id);

        // 删除教师信息
        teacherInformationMapper.deleteById(id);

    }

    @Override
    public TeacherInformationDO getTeacherInformation(Long id) {
        TeacherInformationDO teacher = teacherInformationMapper.selectById(id);
        String contactInformation = teacher.getContactInformation();
        if (contactInformation != null) {
            String encryptedContactInformation = desensitizeEncrypt((contactInformation));
            teacher.setContactInformation(encryptedContactInformation);
        }
        String idNumber = teacher.getIdNumber();
        if (idNumber != null) {
            String encryptedIdNumber = desensitizeEncrypt((idNumber));
            teacher.setIdNumber(encryptedIdNumber);
        }
        String professionalTitle = teacher.getProfessionalTitle();
        if (professionalTitle != null) {
            String encryptedProfessionalTitle = desensitizeEncrypt((professionalTitle));
            teacher.setProfessionalTitle(encryptedProfessionalTitle);
        }
        return teacher;
    }

    /**
     * 导出校外师资导入模板
     *
     * @param request  请求
     * @param response 响应
     */
    @Override
    public void exportImportTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }

        // 获取性别、民族、政治面貌、学历、学位、职级、行政级别所有字典选项
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(TeacherInformationDictEnum.getTypeList()).getCheckedData();

        // 获取所有字典类型 -> 字典选项 映射
        Map<String, List<String>> dictTypeToLabelMap = new HashMap<>();
        for (TeacherInformationDictEnum dictEnum : TeacherInformationDictEnum.values()) {
            // 学位是树字典，跳过
            if (TeacherInformationDictEnum.PERSON_ACADEMIC_DEGREE.getType().equals(dictEnum.getType())){
                continue;
            }
            List<String> dataRespDTOS = dictDataList.stream().filter(dictData -> dictEnum.getType().equals(dictData.getDictType()))
                    .map(DictDataRespDTO::getLabel).collect(Collectors.toList());
            dictTypeToLabelMap.put(dictEnum.getType(), dataRespDTOS);
        }
        // 学位是树字典
        List<DictDataRespDTO> degreeList = dictDataList.stream().filter(dictData -> TeacherInformationDictEnum.PERSON_ACADEMIC_DEGREE.getType().equals(dictData.getDictType()))
                .collect(Collectors.toList());
        // 获取学位完整字典
        List<String> degreeLabelList = TreeDataUtil.generateSortedFullPathList(packageDictDataAsTreeNodeDTOList(degreeList));

        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request)).getCheckedData();
        // 获取所有管理部门的完整路径列表
        List<String> deptLabelList = TreeDataUtil.generateSortedFullPathList(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单 deptLabelList:{}", deptLabelList);

        // 生成下拉列表Map关系 CoursesImportExcelVO 列索引值 ->  下拉框内容列表
        Map<Integer, List<String>> dictDataMap = new HashMap<>();
        // 民族
        dictDataMap.put(2, dictTypeToLabelMap.get(TeacherInformationDictEnum.NATION.getType()));
        // 性别
        dictDataMap.put(3, dictTypeToLabelMap.get(TeacherInformationDictEnum.SYSTEM_USER_SEX.getType()));
        // 政治面貌
        dictDataMap.put(4, dictTypeToLabelMap.get(TeacherInformationDictEnum.POLITICAL_IDENTITY.getType()));
        // 学历
        dictDataMap.put(12, dictTypeToLabelMap.get(TeacherInformationDictEnum.PERSON_EDUCATION.getType()));
        // 学位
        dictDataMap.put(13, degreeLabelList);
        // 职级
        dictDataMap.put(14, dictTypeToLabelMap.get(TeacherInformationDictEnum.PERSON_RANK.getType()));
        // 行政级别
        dictDataMap.put(16, dictTypeToLabelMap.get(TeacherInformationDictEnum.PERSON_ADMINISTRATIVE_POSITION_RANK.getType()));
        // 部门
        dictDataMap.put(17, deptLabelList);
        List<TeacherInformationImportExcelVO> list = new ArrayList<>();
        // 插入模板

        ExcelUtils.write(response, "师资库导入模板.xlsx",
                "信息",
                TeacherInformationImportExcelVO.class,
                list, // 模板无数据
                dictDataMap, // 下拉
                null); // 忽略错误信息列
    }

    private List<TreeNodeDTO> packageDeptDataAsTreeNodeDTOList(List<DeptSimpleRespDTO> list) {
        return list.stream().map(data -> new TreeNodeDTO(data.getId(), data.getParentId(), data.getName())).collect(Collectors.toList());
    }


    /**
     * 外校师资导入
     *
     * @param request 请求
     * @param list    导入数据
     * @return 导入结果
     */
    @Override
    public ExcelImportResultRespVO importTeacherInformation(HttpServletRequest request, List<TeacherInformationImportResultExcelVO> list) {
        Long tenantId = getTenantId();
        log.info("当前租户ID：{}", tenantId);
        if (Objects.isNull(tenantId)) {
            throw exception(COURSE_TENANT_NOT_EXISTS);
        }

        // 获取性别、民族、政治面貌、学历、学位、职级、行政级别所有字典选项
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(TeacherInformationDictEnum.getTypeList()).getCheckedData();

        // 获取所有字典类型 -> Map<label->value> 映射
        Map<String, Map<String,String>> dictTypeToMap = new HashMap<>();
        for (TeacherInformationDictEnum dictEnum : TeacherInformationDictEnum.values()) {
            // 学位是树字典，跳过
            if (TeacherInformationDictEnum.PERSON_ACADEMIC_DEGREE.getType().equals(dictEnum.getType())){
                continue;
            }
            Map<String, String> labelToValueMap = dictDataList.stream().filter(dictData -> dictEnum.getType().equals(dictData.getDictType()))
                    .collect(Collectors.toMap(DictDataRespDTO::getLabel, DictDataRespDTO::getValue, (v1, v2) -> v1));
            dictTypeToMap.put(dictEnum.getType(), labelToValueMap);
        }
        List<DeptSimpleRespDTO> deptSimpleRespDTOList = businessCenterApi.getDept(tenantId, null, getTokenFromRequestHead(request))
                .getCheckedData();
        // 获取所有管理部门的id->完整路径Map
        Map<String, Long> fullPathMap2deptId = TreeDataUtil.generateFullPathToIdMap(packageDeptDataAsTreeNodeDTOList(deptSimpleRespDTOList));
        log.info("人事系统部门菜单:{}", fullPathMap2deptId);
        // 所有管理部门id -> 部门名称
        Map<Long, String> deptId2deptName = deptSimpleRespDTOList.stream().collect(Collectors.toMap(DeptSimpleRespDTO::getId, DeptSimpleRespDTO::getName));
        // 学位完整路径名->学位字典id
        // 学位是树字典
        List<DictDataRespDTO> degreeList = dictDataList.stream().filter(dictData -> TeacherInformationDictEnum.PERSON_ACADEMIC_DEGREE.getType().equals(dictData.getDictType()))
                .collect(Collectors.toList());
        Map<String, Long> fullPathMap2degreeId = TreeDataUtil.generateFullPathToIdMap(packageDictDataAsTreeNodeDTOList(degreeList));

        // 需要新增的教师列表 全部新增
        List<TeacherInformationDO> addList = new ArrayList<>();

        // 错误列表
        List<TeacherInformationImportResultExcelVO> errorList = new ArrayList<>();

        for (TeacherInformationImportResultExcelVO vo : list){
            if (Objects.isNull(vo.getError())) {
                vo.setError("");
            }

            // 校验姓名
            if (StringUtils.isBlank(vo.getName())) {
                vo.setError(vo.getError() + TEACHER_IMPORT_NAME_REQUIRED.getMsg());
            }else {
                // 字符小于等于30
                if (vo.getName().length() > 30) {
                    vo.setError(vo.getError() + TEACHER_IMPORT_NAME_LENGTH_LIMIT.getMsg());
                }
            }

            // // 校验身份证格式
            // if (StringUtils.isNotBlank(vo.getIdNumber()) && !CheckIdCard.check(vo.getIdNumber())){
            //     vo.setError(vo.getError() + TEACHER_IMPORT_ID_NUMBER_FORMAT_ERROR.getMsg());
            // }

            LocalDate birthday = null;
            // 出生日期校验 yyyy-MM-dd
            if (StringUtils.isNotBlank(vo.getBirthdayStr())) {
                try {
                    birthday = DateUtils.parseLocalDate(vo.getBirthdayStr(), FORMAT_YEAR_MONTH_DAY);
                } catch (Exception e) {
                    vo.setError(vo.getError() + TEACHER_IMPORT_BIRTHDAY_FORMAT_ERROR.getMsg());
                }
            }

            // 民族
            Integer nation = null;
            String nationStr = getTeacherDictValueAndSetError(vo, vo.getNationStr(), dictTypeToMap.get(TeacherInformationDictEnum.NATION.getType()),
                    TEACHER_IMPORT_NATION_NOT_EXISTS.getMsg());
            if (StringUtils.isNotBlank(nationStr)){
                nation = Integer.valueOf(nationStr);
            }

            // 性别
            Integer gender = null;
            String genderStr = getTeacherDictValueAndSetError(vo, vo.getGenderStr(), dictTypeToMap.get(TeacherInformationDictEnum.SYSTEM_USER_SEX.getType()),
                    TEACHER_IMPORT_GENDER_NOT_EXISTS.getMsg());
            if (StringUtils.isNotBlank(genderStr)){
                gender = Integer.valueOf(genderStr);
            }

            // 政治面貌
            Integer politicalStatus = null;
            String politicalStatusStr = getTeacherDictValueAndSetError(vo, vo.getPoliticalStatusStr(), dictTypeToMap.get(TeacherInformationDictEnum.POLITICAL_IDENTITY.getType()),
                    TEACHER_IMPORT_POLITICAL_STATUS_NOT_EXISTS.getMsg());
            if (StringUtils.isNotBlank(politicalStatusStr)){
                politicalStatus = Integer.valueOf(politicalStatusStr);
            }

            // 籍贯 限制50字符
            if (StringUtils.isNotBlank(vo.getNativePlace()) && vo.getNativePlace().length() > 50) {
                vo.setError(vo.getError() + TEACHER_IMPORT_NATIVE_PLACE_LENGTH_LIMIT.getMsg());
            }

            // 来校年月
            LocalDate arrivalTime = null;
            if (StringUtils.isNotBlank(vo.getArrivalTimeStr())) {
                try {
                    arrivalTime = DateUtils.parseLocalDate(vo.getArrivalTimeStr(), FORMAT_YEAR_MONTH_DAY);
                } catch (Exception e) {
                    vo.setError(vo.getError() + TEACHER_IMPORT_ARRIVAL_TIME_FORMAT_ERROR.getMsg());
                }
            }

            // 联系方式
            if (StringUtils.isNotBlank(vo.getContactInformation())
                    && !ValidateMatchUtils.isTel(vo.getContactInformation())
                    && !ValidateMatchUtils.isMobileSimple(vo.getContactInformation())) {
                vo.setError(vo.getError() + TEACHER_IMPORT_CONTACT_INFORMATION_FORMAT_ERROR.getMsg());
            }

            // 通讯地址
            if (StringUtils.isNotBlank(vo.getMailingAddress()) && vo.getMailingAddress().length() > 100) {
                vo.setError(vo.getError() + TEACHER_IMPORT_MAIL_ADDRESS_LENGTH_LIMIT.getMsg());
            }

            // 邮政编码
            if (StringUtils.isNotBlank(vo.getPostalCode()) && !ValidateMatchUtils.isPostCode(vo.getPostalCode())) {
                vo.setError(vo.getError() + TEACHER_IMPORT_POSTAL_CODE_FORMAT_ERROR.getMsg());
            }

            // 电子邮箱
            if (StringUtils.isNotBlank(vo.getEmail()) && !ValidateMatchUtils.isEmail(vo.getEmail())) {
                vo.setError(vo.getError() + TEACHER_IMPORT_EMAIL_FORMAT_ERROR.getMsg());
            }
            // 所在单位
            if (StringUtils.isNotBlank(vo.getWorkUnit()) && vo.getWorkUnit().length() > 50) {
                vo.setError(vo.getError() + TEACHER_IMPORT_WORK_UNIT_LENGTH_LIMIT.getMsg());
            }
            // 职务
            if (StringUtils.isNotBlank(vo.getProfessionalTitle()) && vo.getProfessionalTitle().length() > 50) {
                vo.setError(vo.getError() + TEACHER_IMPORT_PROFESSIONAL_TITLE_LENGTH_LIMIT.getMsg());
            }
            // 备注
            if (StringUtils.isNotBlank(vo.getRemark()) && vo.getRemark().length() > 100) {
                vo.setError(vo.getError() + TEACHER_IMPORT_REMARK_LENGTH_LIMIT.getMsg());
            }

            // 学历
            Integer education = null;
            String educationStr = getTeacherDictValueAndSetError(vo, vo.getEducationStr(), dictTypeToMap.get(TeacherInformationDictEnum.PERSON_EDUCATION.getType()),
                    TEACHER_IMPORT_EDUCATION_NOT_EXISTS.getMsg());
            if (StringUtils.isNotBlank(educationStr)){
                education = Integer.valueOf(educationStr);
            }

            // 职级
            String rank = getTeacherDictValueAndSetError(vo, vo.getRankStr(), dictTypeToMap.get(TeacherInformationDictEnum.PERSON_RANK.getType()),
                    TEACHER_IMPORT_RANK_NOT_EXISTS.getMsg());

            // 行政级别
            String administrativeLevel = getTeacherDictValueAndSetError(vo, vo.getAdministrativeLevel(), dictTypeToMap.get(TeacherInformationDictEnum.PERSON_ADMINISTRATIVE_POSITION_RANK.getType()),
                    TEACHER_IMPORT_ADMINISTRATIVE_LEVEL_NOT_EXISTS.getMsg());

            // 管理部门ID
            Long managementDeptId = null;
            // 校验管理部门
            if (StringUtils.isNotBlank(vo.getDeptNames())) {
                managementDeptId = fullPathMap2deptId.get(vo.getDeptNames());
                if (Objects.isNull(managementDeptId)) {
                    vo.setError(vo.getError() + TEACHER_IMPORT_DEPT_NOT_EXISTS.getMsg());
                }
            }

            // 学位字典ID
            Integer degreeId = null;
            if (StringUtils.isNotBlank(vo.getDegreeStr())) {
                Long degreeIdLong = fullPathMap2degreeId.get(vo.getDegreeStr());
                if (Objects.isNull(degreeIdLong)) {
                    vo.setError(vo.getError() + TEACHER_IMPORT_DEGREE_NOT_EXISTS.getMsg());
                }else {
                    degreeId = degreeIdLong.intValue();
                }
            }

            if (StringUtils.isBlank(vo.getError())) {
                TeacherInformationDO teacherInformationDO = new TeacherInformationDO();
                // 师资来源
                teacherInformationDO.setSource(TeacherInformationSourceEnum.OUTSIDE.getType());
                //  姓名
                teacherInformationDO.setName(vo.getName());
                // // 身份证号
                // teacherInformationDO.setIdNumber(vo.getIdNumber());
                // 出生日期
                teacherInformationDO.setBirthday(birthday);
                // 民族
                teacherInformationDO.setNation(nation);
                // 性别
                teacherInformationDO.setGender(gender);
                // 政治面貌
                teacherInformationDO.setPoliticalStatus(politicalStatus);
                // 籍贯
                teacherInformationDO.setNativePlace(vo.getNativePlace());
                // 来校年月
                teacherInformationDO.setArrivalTime(arrivalTime);
                // 联系方式
                teacherInformationDO.setContactInformation(vo.getContactInformation());
                // 通讯地址
                teacherInformationDO.setMailingAddress(vo.getMailingAddress());
                // 邮政编码
                teacherInformationDO.setPostalCode(vo.getPostalCode());
                // 电子邮箱
                teacherInformationDO.setEmail(vo.getEmail());
                // 所在单位
                teacherInformationDO.setWorkUnit(vo.getWorkUnit());
                // 学历
                teacherInformationDO.setEducation(education);
                // 学位
                teacherInformationDO.setDegree(degreeId);
                // 职务
                teacherInformationDO.setProfessionalTitle(vo.getProfessionalTitle());
                // 职级
                teacherInformationDO.setRank(rank);
                // 行政级别
                teacherInformationDO.setAdministrativeLevel(administrativeLevel);
                // 管理部门
                if (Objects.nonNull(managementDeptId)) {
                    teacherInformationDO.setDeptIds(managementDeptId.toString());
                    teacherInformationDO.setDeptNames(deptId2deptName.get(managementDeptId));
                }else{
                    // 校外兼职教师 无部门时 默认部门
                    teacherInformationDO.setDeptIds(OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_ID);
                    teacherInformationDO.setDeptNames(OUTSIDE_TEACHER_DEFAULT_DEPARTMENT_NAME);
                }
                // 备注
                teacherInformationDO.setRemark(vo.getRemark());
                addList.add(teacherInformationDO);
            }else {
                errorList.add(vo);
            }
        }

        if (!addList.isEmpty()) {
            teacherInformationMapper.insertBatch(addList);
        }

        // 导入失败数量
        int failNumber = errorList.size();
        // 导入成功数量
        int successNumber = list.size() - failNumber;
        log.info("校外师资导入完成，成功：{}，失败：{}", successNumber, failNumber);
        // 错误列表上传url
        String errorExcelUrl = null;
        // 导出错误列表
        if (!errorList.isEmpty()) {
            errorExcelUrl = UploadExcel(errorList, "导入失败明细.xlsx", "导入失败列表");
        }

        return new ExcelImportResultRespVO(successNumber, failNumber, errorExcelUrl, "导入失败明细.xlsx");
    }

    private String getTeacherDictValueAndSetError(TeacherInformationImportResultExcelVO vo,
                                       String fieldStr,
                                       Map<String,String> labelToValueMap,
                                       String errorStr){
        String dictValue;
        if (StringUtils.isNotBlank(fieldStr)){
            dictValue = labelToValueMap.get(fieldStr);
            if (StringUtils.isBlank(dictValue)){
                vo.setError(vo.getError() + errorStr);
            }else {
                return dictValue;
            }
        }
        return null;
    }

    @Override
    public PageResult<TeachingRecordRespVO> getTeachingRecordPage(TeachingRecordReqVO reqVO) {

        // 使用ClassCourseMapper的现有方法查询数据
        List<TeachingRecordRespVO> list = classCourseMapper.getTeachingRecordPage(reqVO);

        // 如果列表为空，直接返回空的分页结果
        if (CollUtil.isEmpty(list)) {
            log.warn("未找到教师授课记录");
            return PageResult.empty();
        }

        // 设置授课时间段
        for (TeachingRecordRespVO record : list) {
            record.setClassduration(formatClassDuration(record.getBeginTime(), record.getEndTime()));
        }

        // 获取总数
        Long total = list.get(0).getTotal();
        log.info("查询到的总记录数: {}", total);

        // 返回分页结果
        return new PageResult<>(list, total != null ? total : 0L);
    }

    /**
     * 格式化授课时间段
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 格式化后的授课时间段，如：2025-07-17 上午 10:22 ~ 11:23
     */
    private String formatClassDuration(LocalDateTime beginTime, LocalDateTime endTime) {
        if (beginTime == null || endTime == null) {
            return "";
        }

        // 获取日期、上下午和时间
        String date = beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String period = getPeriodName(beginTime.getHour());
        String startTime = beginTime.format(DateTimeFormatter.ofPattern("HH:mm"));
        String endTime1 = endTime.format(DateTimeFormatter.ofPattern("HH:mm"));

        // 组合成 "2025-07-17 上午 10:22 ~ 11:23" 格式
        return String.format("%s %s %s ~ %s", date, period, startTime, endTime1);
    }

    /**
     * 根据小时获取时间段名称（上午/下午/晚上）
     *
     * @param hour 小时
     * @return 时间段名称
     */
    private String getPeriodName(int hour) {
        if (hour >= 0 && hour < 12) {
            return "上午";
        } else if (hour >= 12 && hour < 18) {
            return "下午";
        } else {
            return "晚上";
        }
    }

    @Override
    public List<TeachingRecordExcelVO> getTeachingRecordExportList(TeachingRecordReqVO reqVO) {
        // 设置为导出全部数据
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);

        // 查询授课记录列表
        PageResult<TeachingRecordRespVO> pageResult = getTeachingRecordPage(reqVO);

        // 将分页结果转换为Excel导出对象列表
        return convertToExcelVOList(pageResult.getList());
    }

    /**
     * 将TeachingRecordRespVO列表转换为TeachingRecordExcelVO列表
     */
    private List<TeachingRecordExcelVO> convertToExcelVOList(List<TeachingRecordRespVO> list) {
        return list.stream().map(respVO -> {
            TeachingRecordExcelVO excelVO = new TeachingRecordExcelVO();
            excelVO.setSerialNumber(respVO.getSerialNumber());
            excelVO.setCourseName(respVO.getCourseName());
            excelVO.setClassName(respVO.getClassName());
            excelVO.setTeacherName(respVO.getTeacherName());
            excelVO.setBeginTime(respVO.getBeginTime());
            excelVO.setEndTime(respVO.getEndTime());
            excelVO.setClassduration(respVO.getClassduration());
            excelVO.setTeachmethod(respVO.getTeachmethod());
            return excelVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<TeacherInformationDO> getTeacherInformationList(Collection<Long> ids) {
        if(ids == null || ids.isEmpty()) {
            return null;
        }
        List<TeacherInformationDO> results =  teacherInformationMapper.selectBatchIds(ids);
        results.forEach(result -> {
            String rawContactInformation = result.getContactInformation();
            if (rawContactInformation != null) {
                String contactInformation = DesensitizeUtils.mobileDesensitize(rawContactInformation);
                String encryptedContactInformation = desensitizeEncrypt((contactInformation));
                result.setContactInformation(encryptedContactInformation);
            }

            String rawId = result.getIdNumber();
            if (rawId != null) {
                String idCard = DesensitizedUtil.idCardNum(rawId, 5, 2);
                result.setIdNumber(desensitizeEncrypt(idCard));
            }
            String rawProfessionalTitle = result.getProfessionalTitle();
            if (rawProfessionalTitle != null) {
                String professiontalTitle = DesensitizeUtils.sliderDesensitize(rawProfessionalTitle, 1, 0);
                result.setProfessionalTitle(desensitizeEncrypt(professiontalTitle));
            }
        });
        return results;
    }

    @Override
    public PageResult<TeacherInformationRespVO> getTeacherInformationPage(TeacherInformationPageReqVO reqVO) {
        IPage<TeacherInformationRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<TeacherInformationRespVO> pageResult = teacherInformationMapper.selectPageByPageVO(page, reqVO);
        pageResult.forEach(result -> {
            String rawContactInformation = result.getContactInformation();
            if (rawContactInformation != null) {
                String contactInformation = DesensitizeUtils.mobileDesensitize(rawContactInformation);
                String encryptedContactInformation = desensitizeEncrypt((contactInformation));
                result.setContactInformation(encryptedContactInformation);
            }

            String rawId = result.getIdNumber();
            if (rawId != null) {
                String idCard = DesensitizedUtil.idCardNum(rawId, 5, 2);
                result.setIdNumber(desensitizeEncrypt(idCard));
            }
            String rawProfessionalTitle = result.getProfessionalTitle();
            if (rawProfessionalTitle != null) {
                String professiontalTitle = DesensitizeUtils.sliderDesensitize(rawProfessionalTitle, 1, 0);
                result.setProfessionalTitle(desensitizeEncrypt(professiontalTitle));
            }
        });
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(null ,
                page.getTotal(),
                reqVO,
                pageResult.size());
        for (int i = 0; i < pageResult.size(); i++) {
            pageResult.get(i).setSerialNumber(serialNumberList.get(i));
        }
        if(reqVO.getIsSerialDesc() == null || reqVO.getIsSerialDesc() == false) {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(TeacherInformationRespVO::getSerialNumber)
            ).collect(Collectors.toList());
        } else {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(TeacherInformationRespVO::getSerialNumber).reversed()
            ).collect(Collectors.toList());
        }
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public List<TeacherInformationExcelVO> getTeacherInformationExportList(TeacherInformationPageReqVO exportVO) {
        List<TeacherInformationRespVO> list = getExportDataListByIdsOrReqVO(exportVO);
        List<TeacherInformationExcelVO> exportList = TeacherInformationConvert.INSTANCE.convertExportList(list);

        return exportList;
    }


    @Override
    public List<CourseInformationRespVO> getCourseInformationByTeacher(Long id) {
        List<TeacherCourseInformationDO> informationList = teacherCourseInformationMapper.selectListByTeacherId(id);

        // 根据老师id获取授课信息
        LocalDateTime now = LocalDateTime.now(); // get the current date and time
        List<TeacherLectureTimeRespVO> lectureInfo = classCourseMapper.getLectureInfoByTeacherId(id);

        // 已经安排授课的课程id
        List<Long> arrangedCourseId = lectureInfo.stream().map(TeacherLectureTimeRespVO::getCourseId).distinct().collect(Collectors.toList());

        // 筛选出已结束的授课
        List<TeacherLectureTimeRespVO> finishedLecture = lectureInfo.stream()
                .filter(lecture -> lecture.getEndTime() != null && lecture.getEndTime().isBefore(now))
                .collect(Collectors.toList());

        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.THEME.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.EDUCATE_FORM.getType())).getCheckedData();
        // 字典数据列表转树节点用于计算
        List<TreeNodeDTO> treeNodeDTOList =  dictData.stream().map(data -> new TreeNodeDTO(data.getId(), data.getParentId(), data.getLabel())).collect(Collectors.toList());
        // 课程分类、教学形式 字典id->完整路径表示ID
        Map<Long, String> map = TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);
        List<CourseInformationRespVO> results = new ArrayList<>();
        if(informationList != null && !informationList.isEmpty()) {
            // 已有的关联课程ID列表
            Set<Long> existCourseIds = informationList.stream().map(TeacherCourseInformationDO::getCoursesId).collect(Collectors.toSet());

            //课程ID -> 关联关系ID列表
            Map<Long, Long> courseId2InformationIdsMap = informationList.stream()
                    .collect(Collectors.toMap(TeacherCourseInformationDO::getCoursesId, TeacherCourseInformationDO::getId));

            List<CoursesDO> coursesDOS = coursesMapper.selectBatchIds(existCourseIds);

            results = getCourseInformation(coursesDOS, courseId2InformationIdsMap);
        }
        results.forEach(courseInformationRespVO -> {
            if (Objects.nonNull(courseInformationRespVO.getThemeId())) {
                courseInformationRespVO.setTheme(map.get(courseInformationRespVO.getThemeId())); // 课程分类名
            }
            if (Objects.nonNull(courseInformationRespVO.getEducateFormId())) {
                courseInformationRespVO.setEducateForm(map.get(courseInformationRespVO.getEducateFormId())); // 教学形式
            }
            Long count = finishedLecture.stream().filter(info -> info.getCourseId().equals(courseInformationRespVO.getId())).count();
            if (count != null) {
                courseInformationRespVO.setLectureTime(count);
            } else {
                courseInformationRespVO.setLectureTime(0L);
            }
            if (arrangedCourseId.contains(courseInformationRespVO.getId())) {
                courseInformationRespVO.setIsArranged(true);
            } else {
                courseInformationRespVO.setIsArranged(false);
            }
        });
        return results;
    }

    @NotNull
    private static List<CourseInformationRespVO> getCourseInformation(List<CoursesDO> coursesDOS, Map<Long, Long> courseId2InformationIdsMap) {
        List<CourseInformationRespVO> results = new ArrayList<>();
        for (CoursesDO coursesDO : coursesDOS) {
            CourseInformationRespVO courseInformation = new CourseInformationRespVO();
            Long relationId = courseId2InformationIdsMap.get(coursesDO.getId());

            courseInformation.setId(coursesDO.getId());
            courseInformation.setName(coursesDO.getName());
            courseInformation.setCoursesType(coursesDO.getCoursesType());
            courseInformation.setThemeId(coursesDO.getThemeId());
            courseInformation.setEducateFormId(coursesDO.getEducateFormId());

            courseInformation.setRelationId(relationId);

            results.add(courseInformation);

        }
        return results;
    }

    private void validateTeacherExists(Long id) {
        if (teacherInformationMapper.selectById(id) == null) {
            throw exception(TEACHER_NOT_EXISTS);
        }
    }

    private String getTokenFromRequestHead(HttpServletRequest request) {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        // 如果token以"Bearer "开头，则提取实际的token值
        if (token != null && token.startsWith("Bearer ")) {
            // 去除"Bearer "部分
            token = token.substring(7);
        }
        return token;
    }

    private List<TeacherInformationRespVO> getExportDataListByIdsOrReqVO(TeacherInformationPageReqVO exportReqVO) {
        List<TeacherInformationRespVO> teacherInformationList;
        // 如果是勾选导出
        if (Objects.nonNull(exportReqVO.getIds()) && !exportReqVO.getIds().isEmpty()) {
            teacherInformationList = teacherInformationMapper.selectListByIds(exportReqVO.getIds());
        }
        // 如果是条件筛选
        else {
            exportReqVO.setPageNo(1);
            exportReqVO.setPageSize(Integer.MAX_VALUE);
            teacherInformationList = getTeacherInformationPage(exportReqVO).getList();
        }
        return teacherInformationList;
    }



    @Override
    public TeacherSyncResultRespVO syncTeacher(HttpServletRequest request ,Long mytenantId) {
        Long tenantId =mytenantId==null?getTenantId():mytenantId;

        if(tenantId==null){
            return null;
        }

        String token = getTokenFromRequestHead(request);
        List<PersonnalRespDTO> personnals = businessCenterApi.getPersonnal(tenantId, token).getCheckedData();
        List<TeacherInformationDO> teacherInformationRespVOS = teacherInformationMapper.selectTeachersDeleted(tenantId);
        List<String> mobiles = teacherInformationRespVOS.stream().map(TeacherInformationDO::getContactInformation).filter(Objects::nonNull).collect(Collectors.toList());
        personnals=personnals.stream().filter(p->!mobiles.contains(p.getContactInformation())).collect(Collectors.toList());

        try {
            teacherDeptService.syncTeacherDept(tenantId);
        } catch (Exception e) {
            log.info("同步教师部门信息失败，原因：" + e.getMessage(), e);
        }
        return convertTeacherAndInsert(tenantId ,personnals );
    }

    @Override
    public TeacherSyncResultRespVO  convertTeacherAndInsert(Long tenantId, List<PersonnalRespDTO> personnals){
        log.info("同步教师 - convertTeacherAndInsert：" + tenantId);
        if(personnals==null){
           personnals =  businessCenterApi.getPersonnal(tenantId, null).getCheckedData();
        }
        log.info("同步教师 - personnals：" + StrUtil.join("," ,   personnals));
        List<TeacherInformationDO> teachers = TeacherInformationConvert.INSTANCE.convertDTOList(personnals);

        for (int i = 0; i < teachers.size(); i++) {
            teachers.get(i).setUserId(null);
            teachers.get(i).setSystemId(personnals.get(i).getUserId());
        }


        //根据otherSystemId 去重
        Set<TeacherInformationDO> teacherSet = teachers.stream()
                .collect(Collectors.toMap(
                        teacher -> teacher.getSystemId(), // 以 otherSystemId 作为唯一键
                        Function.identity(),
                        (existing, replacement) -> existing  // 如果有重复，保留原始对象
                ))
                .values()
                .stream()
                .collect(Collectors.toSet());



        teachers = new ArrayList<>(teacherSet);

        // 师资来源：校内
        teachers.forEach(teacher -> teacher.setSource(0));

        teachers.forEach(teacher -> teacher.setTenantId(tenantId));

        teachers.forEach(teacher -> {
            if(teacher.getGender() == null) {
                teacher.setGender(3);
            }
        });

        return batchInsert(teachers, tenantId);
    }





    /**
     * 选修课管理-根据选修课发布上课时间段获取空闲下拉教师数据
     *
     * @param reqVO 上课时间段
     * @return 教师列表
     */
    @Override
    public List<TeacherInformationSimpleRespVO> listForElectiveRelease(ElectiveReleaseClassTimeTeacherReqVO reqVO) {
        // 获取上课开始时间、上课结束时间 LocalDateTime
        LocalDateTime classStartTime = LocalDateTime.of(reqVO.getClassDate(), LocalTime.parse(reqVO.getClassStartTimeStr()));
        LocalDateTime classEndTime = LocalDateTime.of(reqVO.getClassDate(), LocalTime.parse(reqVO.getClassEndTimeStr()));
        return teacherInformationMapper.listForElectiveRelease(classStartTime,
                classEndTime,
                reqVO.getExcludeReleaseId(),
                reqVO.getExcludeClassCourseId(),
                reqVO.getCourseId());
    }

    @Override
    public Long getTeacherId(Long userId) {
        LambdaQueryWrapper<TeacherInformationDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeacherInformationDO::getSystemId, userId);
        wrapper.last("limit 1");
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectOne(wrapper);
        if(teacherInformationDO == null) {
            return null;
        }
        return teacherInformationDO.getId();
    }

    @Override
    public List<TeacherInformationRespVO> getTeacherInformationListAll(){
        List<TeacherInformationDO> teacherInformationDOList = this.list();
        return TeacherInformationConvert.INSTANCE.convertList(teacherInformationDOList);
    }



    private TeacherSyncResultRespVO batchInsert(List<TeacherInformationDO> teachers, Long tenantId) {

        log.info("同步教师 - batchInsert：" + tenantId);

        // 获取所有已有老师的信息
        List<TeacherInformationDO> existTeacherList = teacherInformationMapper.selectListByTeanatId(tenantId);
        // 已有老师hrId->id映射
        Map<Long, Long> existTeacherMap = existTeacherList.stream()
                .collect(Collectors.toMap(TeacherInformationDO::getHrId, TeacherInformationDO::getId));

        // hrId集合
        Set<Long> hrIdSet = teachers.stream().map(TeacherInformationDO::getHrId).collect(Collectors.toSet());

        // 已有的hrId集合
        Set<Long> existHrIdSet = existTeacherList.stream().map(TeacherInformationDO::getHrId).collect(Collectors.toSet());

        // 获得需要新增的数据
        Set<Long> toBeInsertedHrIds = hrIdSet.stream()
                .filter(hrId -> !existHrIdSet.contains(hrId))
                .collect(Collectors.toSet());

        // 新增师资信息
        Set<TeacherInformationDO> toBeInsertedTeacher = teachers.stream()
                .filter(teacher -> toBeInsertedHrIds.contains(teacher.getHrId())).collect(Collectors.toSet());


        teacherInformationMapper.insertBatch(toBeInsertedTeacher);
        // 更新已有的师资信息
        List<TeacherInformationDO> toBeUpdatedTeacher = teachers.stream()
                .filter(teacher -> existHrIdSet.contains(teacher.getHrId())).collect(Collectors.toList());
        if(!toBeUpdatedTeacher.isEmpty()) {
            for (TeacherInformationDO teacher : toBeUpdatedTeacher) {
                teacher.setId(existTeacherMap.get(teacher.getHrId()));
            }

            teacherInformationMapper.updateBatch(toBeUpdatedTeacher);
        }


        // 获取不再存在于新教师列表中的教师
        Set<Long> toBeUpdatedTeacherIds = existTeacherList.stream()
                .filter(teacher -> !hrIdSet.contains(teacher.getHrId())) // 找到不在新教师列表中的教师
                .map(TeacherInformationDO::getId) // 获取教师的id
                .collect(Collectors.toSet());

        // 获取需要将hrId置空的教师
        List<TeacherInformationDO> toBeUpdatedTeachers = existTeacherList.stream()
                .filter(teacher -> toBeUpdatedTeacherIds.contains(teacher.getId())) // 根据id找到教师
                .collect(Collectors.toList());

        // 将这些教师的hrId置空
        for (TeacherInformationDO teacher : toBeUpdatedTeachers) {
            teacher.setHrId(null); // 将hrId置空
        }

        // 更新数据库中的教师信息
        if (!toBeUpdatedTeachers.isEmpty()) {
            teacherInformationMapper.updateBatch(toBeUpdatedTeachers); // 更新批量记录
            log.info("将以下教师的hrId置空: " + toBeUpdatedTeacherIds);
        }

        Integer updateNumber = toBeUpdatedTeacher.size();

        Integer insertNumber = toBeInsertedTeacher.size();


        comparisonTeacherUsers(tenantId);


        return new TeacherSyncResultRespVO(insertNumber, updateNumber);
    }


    @Override
    public void comparisonTeacherUsers(Long tenantId) {

        if(tenantId==null){
            return;
        }

        log.info("同步教师 - comparisonTeacherUsers：" + tenantId);

        // 获取所有已有老师的信息
        List<TeacherInformationDO> existTeacherList = teacherInformationMapper.selectListByTeanatId(tenantId);
        List<TeacherInformationDO> informationDOS = existTeacherList.stream()
                .filter(it -> StringUtils.isBlank(it.getContactInformation()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(informationDOS)) {
            throw exception(new ErrorCode(500, "检查所有老师手机号，存在个别老师手机号为空的情况"));
        }
        CreateUserBatchTenantDTO createUserBatchTenantDTO = new CreateUserBatchTenantDTO();
        createUserBatchTenantDTO.setTenantId(tenantId);
        createUserBatchTenantDTO.setUserInputList(TeacherInformatiomnDoConvert.toUserInputDTOList(existTeacherList));

        log.info("同步教师 - 将业中的userid 填充到users表的 systemid" + tenantId);

        //将业中的userid 填充到users表的 systemid
        Map<String , Long> allUserIds = adminUserApi.createUserBatchByTenant(createUserBatchTenantDTO).getCheckedData();



        List<TeacherInformationDO> useridnullist = existTeacherList.stream()
                .filter(it -> it.getContactInformation() != null)
                .filter(it -> allUserIds.containsKey(it.getContactInformation())) // 只保留 allUserIds 中的手机号的数据 ， 不在其中的不进行修改
                .collect(Collectors.toList());


        useridnullist.forEach(it-> {

            it.setUserId(allUserIds.get(it.getContactInformation()));
        });

        log.info("同步教师 -  //把user表的插入用户之后的 id 重新绑定到teacher表" + StrUtil.join("," , useridnullist));

        if(CollUtil.isNotEmpty(useridnullist)){
            //把user表的插入用户之后的 id 重新绑定到teacher表
            teacherInformationMapper.updateBatch(useridnullist);
        }
    }

    @Override
    public void deleteTeacherBySystemId(Long systemId) {
        //edu_teacher_information,system_users
        LambdaUpdateWrapper<TeacherInformationDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(TeacherInformationDO::getSystemId,systemId);
        teacherInformationMapper.delete(lambdaUpdateWrapper);
        adminUserApi.deleteBySystemId(systemId);
    }

    /**
     * 根据系统用户id获取教师id
     * @param systemId 系统用户id
     * @return 教师id
     */
    @Override
    public Long getTeacherBySystemId(Long systemId) {
        LambdaQueryWrapper<TeacherInformationDO> lambdaUpdateWrapper = new LambdaQueryWrapperX<>();
        lambdaUpdateWrapper.eq(TeacherInformationDO::getSystemId,systemId).last("limit 1");
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectOne(lambdaUpdateWrapper);
        if(teacherInformationDO!=null){
            return teacherInformationDO.getId();
        }
        return null;
    }

    /**
     * 缓存仪表盘信息
     */
    public static Cache<UserAppletBaseVO, UserAppletDashboardVO> resultCacheCount=
            CacheBuilder.newBuilder()
                    // 初始容量
                    .initialCapacity(1024)
                    // 设定最大容量
                    .maximumSize(1024*10)
                    // 设定写入过期时间
                    .expireAfterWrite(30L, TimeUnit.MINUTES)
                    // 设置最大并发写操作线程数
                    .concurrencyLevel(8)
                    .build();

    /**
     * 缓存教学培训-课程情况信息
     */
    public static Cache<UserAppletCourseSituationReqVO, UserAppletReultVO> courseSituationResultCacheCount=
            CacheBuilder.newBuilder()
                    // 初始容量
                    .initialCapacity(1024)
                    // 设定最大容量
                    .maximumSize(1024*10)
                    // 设定写入过期时间
                    .expireAfterWrite(5L, TimeUnit.MINUTES)
                    // 设置最大并发写操作线程数
                    .concurrencyLevel(8)
                    .build();

    /**
     * 教学培训-教师个人-仪表盘
     * @param reqVO 开始时间+结束时间
     * @return 教学总工时+平均分+课程统计
     */
    @Override
    public UserAppletDashboardVO getDashboard(UserAppletBaseVO reqVO){
        //todo 简化方法 一个方法不超过80行

        //若存在已缓存数据，则直接返回
        UserAppletDashboardVO result = resultCacheCount.getIfPresent(reqVO);
        if(result!=null){
            return result;
        }

        //获取教学方式字典
        Map<Long, String> dictMap = getDictMap();
        //匹配特殊教学方式字典ID
        Long caseBasedTeaching = dictMap.entrySet().stream()
                .filter(entry -> entry.getValue().equals(CASE_BASED_TEACHING_LABEL))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(-1L);
        Long researchTeaching = dictMap.entrySet().stream()
                .filter(entry -> entry.getValue().equals(RESEARCH_TEACHING_LABEL))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(-1L);

        //根据用户小程序基础信息请求对象获取教师ID列表
        List<Long> teacherIdList = getTeacherIdList(reqVO);

        //专题课总工时
        Integer topicCourseTime;
        //专题课次数
        Integer topicCourseCount;
        //选修课总工时
        Integer optionalCourseTime;
        //选修课次数
        Integer optionalCourseCount;

        //获取课程资源信息
        Map<Long, CoursesDO> coursesMap = getLongCoursesDOMap();
        // 获取该教师在该时段内的"专题课"
        List<ClassCourseDO> classCourseDOUserList = getClassCourseDOUserList(reqVO, coursesMap, teacherIdList);
        //过滤掉为空的数据
        classCourseDOUserList = classCourseDOUserList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //专题课授课次数
        topicCourseCount = classCourseDOUserList.size();
        //开始计算专题课总工时
        // 类型1：（非合班授课、且（教学形式非 案例式教学 或 专题研究式教学 或 多教师授课）为4小时）：根据教师本人统计课程表上的教学时长，按照午别去统计（例如：若该教师某天的上午有课，不管是1节课还是4节课，则统统记为4工时，下午、晚上相同）
        Integer topicCourseTime1 = 0;
        try {
            List<ClassCourseDO> filteredList1 = classCourseDOUserList.stream()
                    .filter(it -> it.getCourseId() != null && coursesMap.get(it.getCourseId()) != null && coursesMap.get(it.getCourseId()).getEducateFormId() != null)
                    .filter(it -> coursesMap.get(it.getCourseId()).getEducateFormId() != null)
                    .filter(it -> !it.getIsMerge())
                    .filter(it -> (!caseBasedTeaching.equals(coursesMap.get(it.getCourseId()).getEducateFormId()) && !researchTeaching.equals(coursesMap.get(it.getCourseId()).getEducateFormId())) || it.getTeacherIdString().contains(","))
                    .collect(Collectors.toList());
            topicCourseTime1 = calculateTotalTeachingHours(filteredList1,teacherIdList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        // 类型2：（非合班授课、且（教学形式为 案例式教学 或 专题研究式教学 且 单教师授课）为8小时）：根据教师本人统计课程表上的教学时长，按照午别去统计（例如：若该教师某天的上午有课，不管是1节课还是4节课，则统统记为4工时，下午、晚上相同）
        Integer topicCourseTime2 = 0;
        try {
            List<ClassCourseDO> filteredList2 = classCourseDOUserList.stream()
                    .filter(it -> it.getCourseId() != null && coursesMap.get(it.getCourseId()) != null && coursesMap.get(it.getCourseId()).getEducateFormId() != null)
                    .filter(it -> !it.getIsMerge())
                    .filter(it -> (caseBasedTeaching.equals(coursesMap.get(it.getCourseId()).getEducateFormId()) || researchTeaching.equals(coursesMap.get(it.getCourseId()).getEducateFormId())) && !it.getTeacherIdString().contains(","))
                    .collect(Collectors.toList());
            topicCourseTime2 = calculateTotalTeachingHours(filteredList2,teacherIdList) * 2;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //类型3：（合班授课：2个班：6工时，3个班：8工时，4个班及以上：10工时，不区分教学形式，所有类型统一计算）
        Integer topicCourseTime3 = 0;
        try {
            List<ClassCourseDO> filteredList3 = classCourseDOUserList.stream()
                    .filter(it -> it.getCourseId() != null && coursesMap.get(it.getCourseId()) != null && coursesMap.get(it.getCourseId()).getEducateFormId() != null)
                    .filter(ClassCourseDO::getIsMerge)
                    .collect(Collectors.toList());
            //记录已经计算过的课程表classCourseID
            Set<Long> processedCourseIds = new HashSet<>();
            for (ClassCourseDO classCourseDO : filteredList3) {
                if (processedCourseIds.contains(classCourseDO.getId())) {
                    continue;
                }
                topicCourseTime3 += calculateTotalTeachingHoursByMerge(classCourseDO, filteredList3 , teacherIdList ,processedCourseIds);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //计算专题课总工时
        topicCourseTime = topicCourseTime1 + topicCourseTime2 + topicCourseTime3;

        // 获取该教师在该时段内的"选修课"
        List<ElectiveReleaseCoursesDO> electiveReleaseCoursesDOListByTeacher = getElectiveReleaseCoursesDOList(reqVO, teacherIdList);
        //按照基础规则计算选修课总工时
        optionalCourseTime = calculateElectiveTeachingHours(electiveReleaseCoursesDOListByTeacher);
        //选修课次数
        optionalCourseCount = electiveReleaseCoursesDOListByTeacher.size();
        //平均分数
        List<EvaluationResponseDO> evaluationResponseDOList = evaluationResponseMapper.selectList(new QueryWrapper<EvaluationResponseDO>().lambda()
                .between(EvaluationResponseDO::getCreateTime, reqVO.getDateBegin(), reqVO.getDateEnd()));
        evaluationResponseDOList = evaluationResponseDOList.stream()
                .filter(it -> it.getTeacherId() != null
                        && teacherIdList.stream().anyMatch(id -> it.getTeacherId().contains(String.valueOf(id)))
                        //增加计算规则：筛选已评卷且必须评价的情况
                        && it.getHandle() && !it.getRemarktype())
                .collect(Collectors.toList());

        //整理信息
        UserAppletDashboardVO userAppletDashboardVO = new UserAppletDashboardVO();
        userAppletDashboardVO.setWorkHourTotal(topicCourseTime + optionalCourseTime);
        userAppletDashboardVO.setCoreCourseTimes(topicCourseCount);
        userAppletDashboardVO.setAlterCourseTimes(optionalCourseCount);
        userAppletDashboardVO.setTopicCourseTime(topicCourseTime);
        userAppletDashboardVO.setOptionalCourseTime(optionalCourseTime);
        if (new BigDecimal(topicCourseCount + optionalCourseCount).compareTo(BigDecimal.ZERO) == 0) {
            // 除数为零时
            userAppletDashboardVO.setCoreCourseProp(BigDecimal.ZERO);
            userAppletDashboardVO.setAlterCourseProp(BigDecimal.ZERO);
        }else {
            userAppletDashboardVO.setCoreCourseProp((new BigDecimal(topicCourseCount).divide(new BigDecimal(topicCourseCount + optionalCourseCount), 2, RoundingMode.HALF_UP)).multiply(BigDecimal.valueOf(100)));
            userAppletDashboardVO.setAlterCourseProp((new BigDecimal(optionalCourseCount).divide(new BigDecimal(topicCourseCount + optionalCourseCount), 2, RoundingMode.HALF_UP)).multiply(BigDecimal.valueOf(100)));
        }
        if (!evaluationResponseDOList.isEmpty()) {
            //求evaluationResponseDOList中的平均分
            BigDecimal avgScore = evaluationResponseDOList.stream()
                    .filter(it -> it.getScore() != null)
                    .map(EvaluationResponseDO::getScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(evaluationResponseDOList.size()), 2, RoundingMode.HALF_UP);
            userAppletDashboardVO.setAvgScore(avgScore);
        }

        //教学方式
        List<UserAppletTeachingMethodsVO> resultList = new ArrayList<>();
        if(CoursesTypeEnum.TOPIC_COURSE.getType().equals(reqVO.getCourseType())){
            //专题课教学方式统计
            // 对课程进行分组，按 `educateFormId` 分类
            Map<Long, List<ClassCourseDO>> groupedByEducateFormId = classCourseDOUserList.stream()
                    .collect(Collectors.groupingBy(course -> {
                        CoursesDO courseInfo = coursesMap.get(course.getCourseId());
                        return courseInfo != null ? courseInfo.getEducateFormId() : null;
                    }));
            //根据教学形式分组的数据、字典映射和总工时来生成统计列表
            getStatList(groupedByEducateFormId, dictMap,resultList,new BigDecimal(topicCourseTime));
        }else if(CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(reqVO.getCourseType())){
            //选修课教学方式统计
            // 对课程进行分组，按 `educateFormId` 分类
            Map<Long, List<ElectiveReleaseCoursesDO>> groupedByEducateFormId = electiveReleaseCoursesDOListByTeacher.stream()
                    .collect(Collectors.groupingBy(course -> {
                        CoursesDO courseInfo = coursesMap.get(course.getCourseId());
                        return courseInfo != null ? courseInfo.getEducateFormId() : null;
                    }));
            //根据教学形式分组的数据、字典映射和总工时来生成统计列表
            getElectiveStatList(groupedByEducateFormId, dictMap,resultList,new BigDecimal(optionalCourseTime));
        }
        userAppletDashboardVO.setTeachingMethods(resultList);

        //计算结果缓存
        resultCacheCount.put(reqVO, userAppletDashboardVO);

        return userAppletDashboardVO;
    }

    /**
     * 业中首页-仪表盘-教学课时
     *
     * @param reqVO 请求参数
     * @return 教学课时
     */
    @Override
    public TeachingHoursForBusinessCenterRespVO getTeachingHours(TeachingHoursForBusinessCenterReqVO reqVO) {

        TeachingHoursForBusinessCenterRespVO teachingHoursForBusinessCenterRespVO = new TeachingHoursForBusinessCenterRespVO();
        teachingHoursForBusinessCenterRespVO.setWorkHoursTotal(0);
        teachingHoursForBusinessCenterRespVO.setCourseTimes(0L);
        // 限定课程结束时间
        reqVO.setCourseEndTime(LocalDateTime.now());
        try {
            // 年度转为开始时间-结束时间
            if (Objects.nonNull(reqVO.getYear())) {
                reqVO.setStartTime(LocalDate.of(reqVO.getYear(), Month.JANUARY, 1));
                reqVO.setEndTime(LocalDate.of(reqVO.getYear(), Month.DECEMBER, 31));
            }

            // ===========专题课工时统计=============
            //1、获取所有课程-教师信息
            List<TeacherInformationClassCourseDTO> teacherTopicClassCourseDTOList = classCourseMapper.selectTopicListJoinTeacher(reqVO);

            // 获取课程分类 教学形式 业中同步过来的字典数据
            List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Collections.singletonList(CoursesDictEnum.EDUCATE_FORM.getType())).getCheckedData();
            // 字典数据列表转树节点用于计算
            List<TreeNodeDTO> treeNodeDTOList = packageDictDataAsTreeNodeDTOList(dictData);
            // 教学形式->完整路径表示ID
            Map<Long, String> idToFullPathMap = TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);

            //匹配特殊教学方式字典ID
            // 案例式
            Long caseBasedTeachingId = idToFullPathMap.entrySet().stream()
                    .filter(entry -> entry.getValue().equals(CASE_BASED_TEACHING_LABEL))
                    .map(Map.Entry::getKey)
                    .findFirst()
                    .orElse(-1L);

            // 专题研究式
            Long researchTeachingId = idToFullPathMap.entrySet().stream()
                    .filter(entry -> entry.getValue().equals(RESEARCH_TEACHING_LABEL))
                    .map(Map.Entry::getKey)
                    .findFirst()
                    .orElse(-1L);

            // 特殊教学形式基本分数
            Map<Long, Integer> specialTeachingFormBaseScoreMap = new HashMap<>();
            specialTeachingFormBaseScoreMap.put(caseBasedTeachingId, 8);
            specialTeachingFormBaseScoreMap.put(researchTeachingId, 8);

            // 获取专题课教师工时信息列表
            List<TeacherTeachingTimeInfoDTO> topicTeachingTimeInfoDTOList = getTopicTeacherTeachingTimeInfoDTOS(teacherTopicClassCourseDTOList, specialTeachingFormBaseScoreMap);
            // 根据请求参数筛选 获取专题课教师工时信息、培训课程数
            TeachingHoursForBusinessCenterRespVO topicTeachingHoursRespVO = getTeachingHoursAndCourseTimes(reqVO, topicTeachingTimeInfoDTOList, teacherTopicClassCourseDTOList);
            // ===========专题课工时统计=============


            // ===========选修课课工时统计=============
            // 1、获取所有课程-教师信息
            List<TeacherInformationClassCourseDTO> teacherOptionalClassCourseDTOList = classCourseMapper.selectOptionalListJoinTeacher(reqVO);
            // 教师选修课工时信息列表
            List<TeacherTeachingTimeInfoDTO> optionalTeachingTimeInfoDTOList = new ArrayList<>();
            teacherOptionalClassCourseDTOList.forEach(dto -> {
                // 选修课教师授课时间信息
                TeacherTeachingTimeInfoDTO timeInfoDTO = new TeacherTeachingTimeInfoDTO();
                timeInfoDTO.setTeacherId(dto.getTeacherId());
                timeInfoDTO.setClassCourseId(dto.getClassCourseId());
                timeInfoDTO.setTeacherDeptId(dto.getTeacherDeptId());
                timeInfoDTO.setTeacherId(dto.getTeacherId());
                timeInfoDTO.setEducateFormId(dto.getEducateFormId());
                // 单班授课下 该课程如果是案例式、专题研究式 基础工时为8
                Integer specialTeachingTime = specialTeachingFormBaseScoreMap.get(dto.getEducateFormId());
                if (Objects.nonNull(specialTeachingTime)) {
                    timeInfoDTO.setTeachingTime(specialTeachingTime);
                } else {
                    // 否则基础工时为4
                    timeInfoDTO.setTeachingTime(4);
                }
                optionalTeachingTimeInfoDTOList.add(timeInfoDTO);
            });

            // 根据请求参数筛选 获取选修课课教师工时信息、培训课程数
            TeachingHoursForBusinessCenterRespVO optionalTeachingHoursRespVO = getTeachingHoursAndCourseTimes(reqVO,
                    optionalTeachingTimeInfoDTOList, teacherOptionalClassCourseDTOList);
            // ===========选修课课工时统计=============

            // 计算总工时
            Integer workHoursTotal = topicTeachingHoursRespVO.getWorkHoursTotal() + optionalTeachingHoursRespVO.getWorkHoursTotal();
            // 计算培训课程数
            Long courseTimesTotal = topicTeachingHoursRespVO.getCourseTimes() + optionalTeachingHoursRespVO.getCourseTimes();
            teachingHoursForBusinessCenterRespVO.setWorkHoursTotal(workHoursTotal);
            teachingHoursForBusinessCenterRespVO.setCourseTimes(courseTimesTotal);
        }catch (Exception e){
            log.info("业中首页-仪表盘-教学课时获取异常", e);
        }
        return teachingHoursForBusinessCenterRespVO;
    }


    /**
     * 根据请求参数和传入的数据，计算并返回专题课的工时和课程次数信息
     *
     * @param reqVO 请求参数对象，包含查询条件
     * @param topicTeachingTimeInfoDTOList 专题课教师授课时间信息列表
     * @param teacherClassCourseDTOList 专题课教师授课课程信息列表
     * @return TeachingHoursForBusinessCenterRespVO 对象，包含专题课的工时和课程次数信息
     */
    private TeachingHoursForBusinessCenterRespVO getTeachingHoursAndCourseTimes(TeachingHoursForBusinessCenterReqVO reqVO,
                                                                                List<TeacherTeachingTimeInfoDTO> topicTeachingTimeInfoDTOList,
                                                                                List<TeacherInformationClassCourseDTO> teacherClassCourseDTOList) {
        int totalTeachingHours;
        long totalClassCount;

        // 下钻级别
        switch (reqVO.getResType()) {
            case 0:
                // 全部门
                // 计算课总工时
                totalTeachingHours = topicTeachingTimeInfoDTOList.stream()
                        .mapToInt(TeacherTeachingTimeInfoDTO::getTeachingTime)
                        .sum();
                // 计算总课数
                totalClassCount = teacherClassCourseDTOList.stream()
                        .map(TeacherInformationClassCourseDTO::getClassCourseId).distinct().count();
                break;
            case 1:
                // 指定部门
                totalTeachingHours = topicTeachingTimeInfoDTOList.stream()
                        .filter(dto -> Objects.equals(dto.getTeacherDeptId(), reqVO.getDeptId()))
                        .mapToInt(TeacherTeachingTimeInfoDTO::getTeachingTime)
                        .sum();
                totalClassCount = teacherClassCourseDTOList.stream()
                        .filter(dto -> Objects.equals(dto.getTeacherDeptId(), reqVO.getDeptId()))
                        .map(TeacherInformationClassCourseDTO::getClassCourseId).distinct().count();
                break;
            case 2:
                // 根据业中systemId 查询教师信息
                TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectBySystemId(reqVO.getTeacherSystemId());
                if (Objects.isNull(teacherInformationDO)) {
                    log.info("业中首页-仪表盘-教学工时统计:根据systemId查询教师信息为空，systemId:{}", reqVO.getTeacherSystemId());
                    totalTeachingHours = 0;
                    totalClassCount = 0;
                    break;
                }
                reqVO.setTeacherId(teacherInformationDO.getId());
                // 指定教师
                totalTeachingHours = topicTeachingTimeInfoDTOList.stream()
                        .filter(dto -> Objects.equals(dto.getTeacherId(), reqVO.getTeacherId()))
                        .mapToInt(TeacherTeachingTimeInfoDTO::getTeachingTime)
                        .sum();
                totalClassCount = teacherClassCourseDTOList.stream()
                        .filter(dto -> Objects.equals(dto.getTeacherId(), reqVO.getTeacherId()))
                        .map(TeacherInformationClassCourseDTO::getClassCourseId).distinct().count();
                break;
            default:
                totalTeachingHours = 0;
                totalClassCount = 0;
        }

        TeachingHoursForBusinessCenterRespVO teachingHoursRespVO = new TeachingHoursForBusinessCenterRespVO();
        teachingHoursRespVO.setWorkHoursTotal(totalTeachingHours);
        teachingHoursRespVO.setCourseTimes(totalClassCount);
        return teachingHoursRespVO;
    }

    /**
     * 获取专题课教师工时信息列表
     * @param teacherClassCourseDTOList 专题课教师信息列表
     * @param specialTeachingFormBaseScoreMap 特殊教学方式基础分数map
     * @return 专题课教师工时信息列表
     */
    private static List<TeacherTeachingTimeInfoDTO> getTopicTeacherTeachingTimeInfoDTOS(List<TeacherInformationClassCourseDTO> teacherClassCourseDTOList,
                                                                                        Map<Long, Integer> specialTeachingFormBaseScoreMap) {

        /**
         * 计算流程
         * <p>
         * 1. 第一步查出来 (排课id , {教师信息}, {排课基本信息} ) 数据teacherClassCourseDTOList
         *    第一步查出来的数据由于一个排课可以存在多个教师 因此存在 多个排课id->不同教师 排课id不唯一
         * 2.拆分a为成单授课列表list1、合班授课列表[合班列表1，合班列表2] list2
         *      合班授课逻辑: 拿到a所有is_merge=1的排课列表 根据 begin_time`end_time（只精确到分钟相等）、classroom_id 分组
         *                  因为存在多教师情况 需要对分组后的的结果列表 根据排课id去重   去重后长度大于2的数据即为合班数据
         * 3.list1根据排课id分组 获得 排课id ->  List[(排课id , {教师信息}, {排课基本信息})] map1
         * 4.获取 是案例式教学、专题研究式教学 的 排课id集合 set1
         * 5. 分别对两个列表做工时计算，最后得到两个 教师基本信息，排课id， 工时 DTO列表
         * </p>
         */

        // 2、拆分单班授课列表list1、合班授课列表[合班列表1，合班列表2] list2
        // 初步筛分合班的课程（isMerge=true）和未合班课程
        Map<Boolean, List<TeacherInformationClassCourseDTO>> partitionedMap = teacherClassCourseDTOList.stream()
                .collect(Collectors.partitioningBy(dto -> Boolean.TRUE.equals(dto.getIsMerge())
                        && Objects.nonNull(dto.getBeginTime())
                        && Objects.nonNull(dto.getEndTime())
                        && Objects.nonNull(dto.getClassRoomId())));
        List<TeacherInformationClassCourseDTO> singleCourses = partitionedMap.get(false);
        List<TeacherInformationClassCourseDTO> mergeCourses = partitionedMap.get(true);

        // 3、对初步筛分后的合班课程进行（上课开始、结束时间、教室）分组
        Map<String, List<TeacherInformationClassCourseDTO>> tempMergeGroupedMap = mergeCourses.stream()
                .collect(Collectors.groupingBy(dto ->
                        truncateToMinute(dto.getBeginTime()) + "-" +
                        truncateToMinute(dto.getEndTime()) + "-" +
                        dto.getClassRoomId()
                ));

        // 教师工时信息列表
        List<TeacherTeachingTimeInfoDTO> teachingTimeInfoDTOList = new ArrayList<>();

        // 计算合班授课教师工时
        tempMergeGroupedMap.forEach((k, dtoList) -> {
            // 相同时间段，相同教室(k)的合班的去重(考虑多授课教师)排课id的数量(即合班数量)
            Set<Long> uniqueClassCourseIds = dtoList.stream()
                    .map(TeacherInformationClassCourseDTO::getClassCourseId)
                    .collect(Collectors.toSet());
            int mergeCount = uniqueClassCourseIds.size();
            // 如果数量为1 则还是单班授课课程 只是因为多教师授课导致的上课时间+教室重复 直接加到单授课列表中
            if (mergeCount == 1) {
                singleCourses.addAll(dtoList);
                return;
            }

            // 长度大于一 说明不是多教师授课的影响 即为合班课程
            //  2个班: 6工时 3个班: 8工时 4个班及以上: 10工时
            int teachingHours;
            // 根据数量返回不同的工时
            // todo 优化魔法值
            if (mergeCount >= 4) {
                teachingHours = 10;
            } else if (mergeCount >= 3) {
                teachingHours = 8;
            } else {
                teachingHours = 6;
            }

            // 已经计算过工时的教师集合
            Set<Long> calculatedTeacherIds = new HashSet<>();

            dtoList.forEach(dto -> {
                // 考虑合班里面可能有一个教师多个班同时授课，但是只计算一次工时
                if (calculatedTeacherIds.contains(dto.getTeacherId())) return;
                // 计算工时
                TeacherTeachingTimeInfoDTO timeInfoDTO = new TeacherTeachingTimeInfoDTO();
                timeInfoDTO.setTeacherId(dto.getTeacherId());
                timeInfoDTO.setClassCourseId(dto.getClassCourseId());
                timeInfoDTO.setTeacherDeptId(dto.getTeacherDeptId());
                timeInfoDTO.setEducateFormId(dto.getEducateFormId());
                timeInfoDTO.setTeachingTime(teachingHours);
                teachingTimeInfoDTOList.add(timeInfoDTO);
                calculatedTeacherIds.add(dto.getTeacherId());
            });
        });

        // 4. 对单班授课课程根据排课id进行分组 获得一节课的上课信息
        Map<Long, List<TeacherInformationClassCourseDTO>> singleGroupedMap = singleCourses.stream()
                .collect(Collectors.groupingBy(TeacherInformationClassCourseDTO::getClassCourseId));

        // 5. 计算单班 教师教学课时
        singleGroupedMap.forEach((classCourseId, dtoList) -> {
            // 该课授课教师数量大于 2 不考虑这节课工时
            if (dtoList.size() > 2) {
                return;
            }
            // 如果是单授课教师
            if (dtoList.size() == 1) {
                TeacherInformationClassCourseDTO dto = dtoList.get(0);
                TeacherTeachingTimeInfoDTO timeInfoDTO = new TeacherTeachingTimeInfoDTO();
                timeInfoDTO.setTeacherId(dto.getTeacherId());
                timeInfoDTO.setClassCourseId(dto.getClassCourseId());
                timeInfoDTO.setTeacherDeptId(dto.getTeacherDeptId());
                timeInfoDTO.setTeacherId(dto.getTeacherId());
                timeInfoDTO.setEducateFormId(dto.getEducateFormId());
                // 单班授课下 该课程如果是案例式、专题研究式 基础工时为8
                Integer specialTeachingTime = specialTeachingFormBaseScoreMap.get(dto.getEducateFormId());
                if (Objects.nonNull(specialTeachingTime)){
                    timeInfoDTO.setTeachingTime(specialTeachingTime);
                }else {
                    // 否则基础工时为4
                    timeInfoDTO.setTeachingTime(4);
                }
                teachingTimeInfoDTOList.add(timeInfoDTO);
            }
            // 如果是多授课教师 平分4
            if (dtoList.size() == 2) {
                for(TeacherInformationClassCourseDTO dto : dtoList){
                    TeacherTeachingTimeInfoDTO timeInfoDTO = new TeacherTeachingTimeInfoDTO();
                    timeInfoDTO.setTeacherId(dto.getTeacherId());
                    timeInfoDTO.setClassCourseId(dto.getClassCourseId());
                    timeInfoDTO.setTeacherDeptId(dto.getTeacherDeptId());
                    timeInfoDTO.setTeacherId(dto.getTeacherId());
                    timeInfoDTO.setEducateFormId(dto.getEducateFormId());
                    // 多班授课 主讲、辅助都是4
                    timeInfoDTO.setTeachingTime(4);
                    teachingTimeInfoDTOList.add(timeInfoDTO);
                }
            }
        });
        return teachingTimeInfoDTOList;
    }

    /**
     * 将时间截取到分钟
     */
    private static LocalDateTime truncateToMinute(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return time.truncatedTo(ChronoUnit.MINUTES);
    }

    /**
     * 用于判断时间是否有交集
     * @param course1
     * @param course2
     * @return 判断结果
     */
    private boolean isTimeOverlap(ClassCourseDO course1, ClassCourseDO course2) {
        return !course1.getBeginTime().isAfter(course2.getEndTime()) &&
                !course1.getEndTime().isBefore(course2.getBeginTime());
    }

    /**
     * 将课程信息转换为基于课程ID的HashMap
     * @return 课程详细信息
     */
    private Map<Long, CoursesDO> getLongCoursesDOMap() {
        //查询该租户下的所有课程资源
        List<CoursesDO> coursesDOList = coursesMapper.selectList();
        //转换为根据id组织的HashMap
        return coursesDOList.stream()
                .collect(Collectors.toMap(CoursesDO::getId, course -> course, (existing, replacement) -> existing));
    }

    /**
     * 根据用户小程序基础信息请求对象获取教师ID列表
     * 此方法的目的是将教师在业务系统中的ID转换为教务系统中的ID
     * @param reqVO 用户小程序基础信息请求对象，包含需要转换的教师业务系统ID列表
     * @return 返回教务系统中的教师ID列表
     */
    private List<Long> getTeacherIdList(UserAppletBaseVO reqVO) {
        List<Long> systemIdList = reqVO.getTeacherIdList();
        //将教师id集合由业中id转换为教务id
        List<TeacherInformationDO> teacherInformationDOS = teacherInformationMapper.selectBySystemIdList(systemIdList);

        //最终的教务老师id集合
        return teacherInformationDOS.stream().map(TeacherInformationDO::getId).collect(Collectors.toList());
    }

    /**
     * 获取该教师在该时段内的"选修课"发布信息
     *
     * @param reqVO         筛选条件
     * @param teacherIdList 教师信息范围
     * @return 选修课排课信息
     */
    private List<ElectiveReleaseDO> getElectiveReleaseDOList(UserAppletBaseVO reqVO, List<Long> teacherIdList) {
        // 获取该教师在该时段内的"选修课"
        List<ElectiveReleaseCoursesDO> electiveReleaseCoursesDOList = electiveReleaseCoursesMapper.selectList();
        List<ElectiveReleaseDO> electiveReleaseDOList = electiveReleaseMapper.selectList();
        List<Long> electiveIdList = electiveReleaseClassesMapper.selectCompleteElectiveIdList();

        // 判断是否为空
        if (CollUtil.isEmpty(electiveIdList)) {
            return new ArrayList<>();
        }

        //教师个人教授选修课
        List<ElectiveReleaseDO> resultList = new ArrayList<>();

        for (ElectiveReleaseCoursesDO electiveReleaseCoursesDO : electiveReleaseCoursesDOList) {
            if (electiveReleaseCoursesDO.getTeacherId() != null && teacherIdList.contains(electiveReleaseCoursesDO.getTeacherId())) {
                for (ElectiveReleaseDO electiveReleaseDO : electiveReleaseDOList) {
                    if (electiveReleaseDO.getId().equals(electiveReleaseCoursesDO.getReleaseId())) {
                        // 时间范围筛选，还要考虑相等的情况
                        if (electiveIdList.contains(electiveReleaseDO.getId())
                                && ((electiveReleaseDO.getClassDate().isBefore(reqVO.getDateEnd())
                                        && electiveReleaseDO.getClassDate().isAfter(reqVO.getDateBegin()))
                                        || (electiveReleaseDO.getClassDate().equals(reqVO.getDateBegin())
                                                || electiveReleaseDO.getClassDate().equals(reqVO.getDateEnd())))) {
                            resultList.add(electiveReleaseDO);
                        }
                    }
                }
            }
        }
        return resultList;
    }


    /**
     * 获取该教师在该时段内的"选修课"课程信息本身
     *
     * @param reqVO         筛选条件
     * @param teacherIdList 教师信息范围
     * @return 选修课排课信息
     */
    private List<ElectiveReleaseCoursesDO> getElectiveReleaseCoursesDOList(UserAppletBaseVO reqVO, List<Long> teacherIdList) {
        // 获取该教师在该时段内的"选修课"
        List<ElectiveReleaseCoursesDO> electiveReleaseCoursesDOList = electiveReleaseCoursesMapper.selectList();
        List<ElectiveReleaseDO> electiveReleaseDOList = electiveReleaseMapper.selectList();

        List<Long> electiveIdList = electiveReleaseClassesMapper.selectCompleteElectiveIdList();

        //教师个人教授选修课
        List<ElectiveReleaseCoursesDO> resultList = new ArrayList<>();

        if (CollUtil.isEmpty(electiveIdList)){
            return resultList;
        }

        for (ElectiveReleaseCoursesDO electiveReleaseCoursesDO : electiveReleaseCoursesDOList) {
            if (electiveReleaseCoursesDO.getTeacherId() != null && teacherIdList.contains(electiveReleaseCoursesDO.getTeacherId())) {
                for (ElectiveReleaseDO electiveReleaseDO : electiveReleaseDOList) {
                    if (electiveReleaseDO.getId().equals(electiveReleaseCoursesDO.getReleaseId())) {
                        //时间范围筛选，还要考虑相等的情况 todo 优化if条件中的复杂表达式
                        if (electiveIdList.contains(electiveReleaseDO.getId()) && ((electiveReleaseDO.getClassDate().isBefore(reqVO.getDateEnd()) && electiveReleaseDO.getClassDate().isAfter(reqVO.getDateBegin()))
                                || (electiveReleaseDO.getClassDate().equals(reqVO.getDateBegin()) || electiveReleaseDO.getClassDate().equals(reqVO.getDateEnd())))) {
                            electiveReleaseCoursesDO.setClassDate(electiveReleaseDO.getClassDate());
                            electiveReleaseCoursesDO.setDayPeriod(electiveReleaseDO.getDayPeriod());
                            resultList.add(electiveReleaseCoursesDO);
                        }
                    }
                }
            }
        }
        return resultList;
    }


    /**
     * 获取该教师在该时段内的"专题课"
     *
     * @param reqVO         筛选条件
     * @param coursesMap    课程信息
     * @param teacherIdList 教师信息范围
     * @return 课表信息
     */
    private List<ClassCourseDO> getClassCourseDOUserList(UserAppletBaseVO reqVO, Map<Long, CoursesDO> coursesMap, List<Long> teacherIdList) {
        //筛选当前生效的教学计划，仅筛选状态为生效的计划
//        List<PlanDO> planList = planService.lambdaQuery().eq(PlanDO::getStatus, PlanStatusEnum.ON.getCode()).list();
//        List<Long> planIds = planList.stream().map(PlanDO::getId).collect(Collectors.toList());
// 获取该教师在该时段内的"专题课"，获取该租户下所有排了课且是专题课的课程，方便后续比较合班授课有多少班
        LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getIsTemporary,false);
        classCourseDOLambdaQueryWrapper.isNotNull(ClassCourseDO::getTeacherIdString);
        //非部门授课+时间范围框定
        classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getDepartment,false);
        classCourseDOLambdaQueryWrapper.ge(ClassCourseDO::getDate, reqVO.getDateBegin().toString());
        classCourseDOLambdaQueryWrapper.le(ClassCourseDO::getDate, reqVO.getDateEnd().toString());
        classCourseDOLambdaQueryWrapper.le(ClassCourseDO::getEndTime, LocalDateTime.now().toString());
//        classCourseDOLambdaQueryWrapper.in(ClassCourseDO::getPlanId,planIds);
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
        //筛选 课程类型为专题课 + 多教师筛选后参与的课程
        return classCourseDOList.stream()
                .filter(it -> it.getCourseId() != null)
                .filter(it -> coursesMap.get(it.getCourseId()) != null)
                .filter(it -> CoursesTypeEnum.TOPIC_COURSE.getType().equals(coursesMap.get(it.getCourseId()).getCoursesType()))
                .filter(it -> teacherIdList.stream().anyMatch(id -> it.getTeacherIdString().contains(String.valueOf(id))))
                .collect(Collectors.toList());
    }


    /**
     * 计算工时的基础规则
     * @param classCourseDOUserList 课表对象
     * @return 工时总时长
     */
    public Integer calculateTotalTeachingHours(List<ClassCourseDO> classCourseDOUserList,List<Long> teacherIdList) {
        // 用一个map来存储每个日期和午别的教学时长（工时），结构为：日期_午别 -> 工时
        Map<String, Integer> teachingHoursMap = new HashMap<>();
        // 遍历每一节课
        for (ClassCourseDO course : classCourseDOUserList) {
            String date = course.getDate();
            String period = course.getPeriod();
            //不同班级的时长分开统计
            String classId = course.getClassId().toString();
            // 拼接日期+午别作为唯一的键（例如：2025-01-21_0代表2025年1月21日上午）
            String key = date + "_" + period + "_" + classId;
            int matchCount = getMatchCount(teacherIdList, course);
            // 判断这个午别是否已经有课程了，如果有则不做处理，如果没有就设置为4工时
            teachingHoursMap.putIfAbsent(key, 4 * matchCount);
        }
        // 计算总工时
        return teachingHoursMap.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 计算教师ID列表与课程教师ID匹配的数量
     * 用于统计在给定教师列表中，有多少教师是该课程的实际授课教师
     *
     * @param teacherIdList 授课教师ID列表
     * @param course 课程信息对象，包含课程的教师ID字符串
     * @return 匹配的教师数量
     */
    private static int getMatchCount(List<Long> teacherIdList, ClassCourseDO course) {
        //多教师授课且筛选多教师，则为多教师的工时相加
        // 将 teacherIdString 分割成字符串数组
        String[] teacherIds = course.getTeacherIdString().split(",");
        List<Long> teacherIdSet = Arrays.stream(teacherIds)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        int matchCount = 0;
        for (Long teacherId : teacherIdList) {
            if (teacherIdSet.contains(teacherId)) {
                matchCount++;
            }
        }
        return matchCount;
    }

    /**
     * 计算选修课的总工时
     * @param electiveReleaseDOListByTeacher 选修课对象
     * @return 工时总时长
     */
    public Integer calculateElectiveTeachingHours(List<ElectiveReleaseCoursesDO> electiveReleaseDOListByTeacher) {
        // 用一个map来存储每个日期和午别的教学时长（工时），结构为：日期_午别 -> 工时
        Map<String, Integer> teachingHoursMap = new HashMap<>();
        // 遍历每一节课
        for (ElectiveReleaseCoursesDO course : electiveReleaseDOListByTeacher) {
            String date = course.getClassDate().toString();
            String period = course.getDayPeriod().toString();
            // 拼接日期+午别作为唯一的键（例如：2025-01-21_0代表2025年1月21日上午）
            String key = date + "_" + period + "_" + course.getTeacherId();
            // 判断这个午别是否已经有课程了，如果有则不做处理，如果没有就设置为4工时
            teachingHoursMap.putIfAbsent(key, 4);
        }
        // 计算总工时
        return teachingHoursMap.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 计算合班授课的总工时
     * @param classCourseDO 课表对象
     * @param classCourseDOUserList 课表对象
     * @return 工时总时长
     */
    public Integer calculateTotalTeachingHoursByMerge(ClassCourseDO classCourseDO , List<ClassCourseDO> classCourseDOUserList , List<Long> teacherIdList , Set<Long> processedCourseIds) {
        List<ClassCourseDO> filteredCourses = classCourseDOUserList.stream()
                .filter(course -> course.getCourseId().equals(classCourseDO.getCourseId()) &&
                        isTimeOverlap(classCourseDO,course)
                ).collect(Collectors.toList());

        for (ClassCourseDO course : filteredCourses) {
            processedCourseIds.add(course.getId());
        }

        long count = filteredCourses.size();

        int matchCount = getMatchCount(teacherIdList, classCourseDO);
        int teachingHours;
        // 根据数量返回不同的工时
        // todo 优化魔法值
        if (count >= 4) {
            teachingHours = 10;
        } else if (count == 3) {
            teachingHours = 8;
        } else if (count >= 1) {
            teachingHours = 6;
        } else {
            // 没有找到匹配的课程，返回0工时
            teachingHours = 0;
        }
        return teachingHours * matchCount;
    }



    /**
     * 教学培训-教学方式
     * @param reqVO 课程类型+开始时间+结束时间
     * @return 教学方式
     */
    @Override
    public List<UserAppletTeachingMethodsVO> getTeachingMethods(UserAppletBaseVO reqVO){
        //获取教学方式字典
        Map<Long, String> dictMap = getDictMap();
        //获取专题课、选修课各自的总工时
        UserAppletDashboardVO dashboard = getDashboard(reqVO);
        //专题课总工时
        Integer topicCourseTime = dashboard.getTopicCourseTime();
        //选修课总工时
        Integer optionalCourseTime = dashboard.getOptionalCourseTime();
        //根据用户小程序基础信息请求对象获取教师ID列表
        List<Long> teacherIdList = getTeacherIdList(reqVO);
        List<UserAppletTeachingMethodsVO> resultList = new ArrayList<>();
        //获取课程资源信息
        Map<Long, CoursesDO> coursesMap = getLongCoursesDOMap();
        if(CoursesTypeEnum.TOPIC_COURSE.getType().equals(reqVO.getCourseType())){
            //专题课教学方式统计
            // 获取该教师在该时段内的"专题课"
            List<ClassCourseDO> classCourseDOUserList = getClassCourseDOUserList(reqVO, coursesMap, teacherIdList);
            // 对课程进行分组，按 `educateFormId` 分类
            Map<Long, List<ClassCourseDO>> groupedByEducateFormId = classCourseDOUserList.stream()
                    .collect(Collectors.groupingBy(course -> {
                        CoursesDO courseInfo = coursesMap.get(course.getCourseId());
                        return courseInfo != null ? courseInfo.getEducateFormId() : null;
                    }));
            //根据教学形式分组的数据、字典映射和总工时来生成统计列表
            getStatList(groupedByEducateFormId, dictMap,resultList,new BigDecimal(topicCourseTime));
        }else if(CoursesTypeEnum.OPTIONAL_COURSE.getType().equals(reqVO.getCourseType())){
            //选修课教学方式统计
            // 获取该教师在该时段内的"选修课"
            List<ElectiveReleaseCoursesDO> electiveReleaseCoursesDOListByTeacher = getElectiveReleaseCoursesDOList(reqVO, teacherIdList);
            // 对课程进行分组，按 `educateFormId` 分类
            Map<Long, List<ElectiveReleaseCoursesDO>> groupedByEducateFormId = electiveReleaseCoursesDOListByTeacher.stream()
                    .collect(Collectors.groupingBy(course -> {
                        CoursesDO courseInfo = coursesMap.get(course.getCourseId());
                        return courseInfo != null ? courseInfo.getEducateFormId() : null;
                    }));
            //根据教学形式分组的数据、字典映射和总工时来生成统计列表
            getElectiveStatList(groupedByEducateFormId, dictMap,resultList,new BigDecimal(optionalCourseTime));
        }
        return resultList;
    }

    /**
     * 专题课：根据教学形式分组的数据、字典映射和总工时来生成统计列表
     *
     * @param groupedByEducateFormId 按教学形式ID分组的课程列表
     * @param dictMap 教学形式ID与名称的字典映射
     * @param resultList 结果列表，包含统计信息的对象
     * @param totalWorkHours 总工时
     */
    private static void getStatList(Map<Long, List<ClassCourseDO>> groupedByEducateFormId, Map<Long, String> dictMap, List<UserAppletTeachingMethodsVO> resultList , BigDecimal totalWorkHours) {
        // 遍历分组后的数据
        for (Map.Entry<Long, List<ClassCourseDO>> entry : groupedByEducateFormId.entrySet()) {
            Long educateFormId = entry.getKey();
            List<ClassCourseDO> classCourseDOList = entry.getValue();
            // 计算每个 educateFormId 对应的工时
            int workHour = classCourseDOList.size() * 4;
            // 获取 educateFormId 对应的名称（从 coursesMap 中获取对应的教学方式名称）
            String educateFormName = dictMap.get(educateFormId);
            // 计算百分比
            BigDecimal percentage = totalWorkHours.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    new BigDecimal(workHour).divide(totalWorkHours, 2, RoundingMode.HALF_UP);
            percentage = percentage.multiply(new BigDecimal("100"));
            // 构建 UserAppletTeachingMethodsVO 对象
            UserAppletTeachingMethodsVO vo = new UserAppletTeachingMethodsVO();
            vo.setEDUCATE_FORM_ID(String.valueOf(educateFormId));
            vo.setEDUCATE_FORM_NAME(educateFormName);
            vo.setProp(percentage);
            vo.setWork_hour(workHour);
            // 添加到结果列表中
            resultList.add(vo);
        }
    }

    /**
     * 选修课：根据教学形式分组的数据、字典映射和总工时来生成统计列表
     *
     * @param groupedByEducateFormId 按教学形式ID分组的课程列表
     * @param dictMap 教学形式ID与名称的字典映射
     * @param resultList 结果列表，包含统计信息的对象
     * @param totalWorkHours 总工时
     */
    private static void getElectiveStatList(Map<Long, List<ElectiveReleaseCoursesDO>> groupedByEducateFormId, Map<Long, String> dictMap , List<UserAppletTeachingMethodsVO> resultList , BigDecimal totalWorkHours) {
        // 遍历分组后的数据
        for (Map.Entry<Long, List<ElectiveReleaseCoursesDO>> entry : groupedByEducateFormId.entrySet()) {
            Long educateFormId = entry.getKey();
            List<ElectiveReleaseCoursesDO> classCourseDOList = entry.getValue();
            // 计算每个 educateFormId 对应的工时
            int workHour = classCourseDOList.size() * 4;
            // 获取 educateFormId 对应的名称（从 coursesMap 中获取对应的教学方式名称）
            String educateFormName = dictMap.get(educateFormId);
            // 计算百分比
            BigDecimal percentage = totalWorkHours.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    new BigDecimal(workHour).divide(totalWorkHours, 2, RoundingMode.HALF_UP);
            percentage = percentage.multiply(new BigDecimal("100"));
            // 构建 UserAppletTeachingMethodsVO 对象
            UserAppletTeachingMethodsVO vo = new UserAppletTeachingMethodsVO();
            vo.setEDUCATE_FORM_ID(String.valueOf(educateFormId));
            vo.setEDUCATE_FORM_NAME(educateFormName);
            vo.setProp(percentage);
            vo.setWork_hour(workHour);
            // 添加到结果列表中
            resultList.add(vo);
        }
    }

    /**
     * 获取字典数据映射
     * 从字典数据中获取课程分类、教学形式等信息，并将其组织成树结构，以便于后续处理
     * @return 字典数据映射，其中键为字典项的ID，值为该字典项的完整路径表示
     */
    private Map<Long, String> getDictMap() {
        // 获取课程分类 教学形式 教学方式 业中同步过来的字典数据
        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Arrays.asList(CoursesDictEnum.THEME.getType(), CoursesDictEnum.OPTIONAL_THEME.getType(),
                CoursesDictEnum.EDUCATE_FORM.getType(), CoursesDictEnum.TEACHING_METHOD.getType())).getCheckedData();
        // 字典数据列表转树节点用于计算
        List<TreeNodeDTO> treeNodeDTOList = packageDictDataAsTreeNodeDTOList(dictData);
        // 课程分类、教学形式 字典id->完整路径表示ID
        return TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);
    }


    /**
     * 将字典数据列表转换为树节点列表
     *
     * @param list 字典数据列表
     * @return 树节点列表
     */
    public List<TreeNodeDTO> packageDictDataAsTreeNodeDTOList(List<DictDataRespDTO> list) {
        return list.stream().map(data -> new TreeNodeDTO(data.getId(), data.getParentId(), data.getLabel())).collect(Collectors.toList());
    }

    /**
     * 教学培训-课程情况
     * @param userAppletBaseVO 开始时间+结束时间
     * @return 课程情况
     */
    @Override
    public UserAppletReultVO getCourseSituation(UserAppletCourseSituationReqVO userAppletBaseVO){
        //若存在已缓存数据，则直接返回
        UserAppletReultVO result = courseSituationResultCacheCount.getIfPresent(userAppletBaseVO);
        if(result!=null){
            return result;
        }

        //根据用户小程序基础信息请求对象获取教师ID列表
        List<Long> teacherIdList = getTeacherIdList(userAppletBaseVO);
        //调用现有课程情况方法
        MyEvaluationPageReqVO reqVO = new MyEvaluationPageReqVO();
        reqVO.setIsTeacher(true);
        reqVO.setStartTime(userAppletBaseVO.getDateBegin());
        reqVO.setEndTime(userAppletBaseVO.getDateEnd());
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setManyIds(null);
        reqVO.setPageNo(userAppletBaseVO.getPageNo());
        reqVO.setPageSize(userAppletBaseVO.getPageSize());
        reqVO.setSetEducateForm(false);

        //只查询已完成的课程
//        reqVO.setCourseEndTime(LocalDateTime.now());

        //是否多教师（部门查询）
        reqVO.setTeacherIdList(teacherIdList);

        PageResult<MyEvaluationPageRespVO> pageResult = evaluationResponseService.getMyEvaluationPage(reqVO);

        //组装适配黑分数据结构
        List<UserAppletCourseSituationVO> userAppletCourseSituationVOList = new ArrayList<>();

        pageResult.getList().forEach(item -> {
            UserAppletCourseSituationVO userAppletCourseSituationVO = new UserAppletCourseSituationVO();
            userAppletCourseSituationVO.setClassName(item.getClassName());
            userAppletCourseSituationVO.setCourseName(item.getCourseName());
            userAppletCourseSituationVO.setAvgScoreR(new BigDecimal(String.valueOf(item.getAverageScore())));
            userAppletCourseSituationVO.setRn(item.getRankStr());
            userAppletCourseSituationVOList.add(userAppletCourseSituationVO);
        });

        UserAppletReultVO userAppletReultVO = new UserAppletReultVO();
        userAppletReultVO.setData(userAppletCourseSituationVOList);
        userAppletReultVO.setTotal(pageResult.getTotal().intValue());
        // 若不存在已缓存数据，则缓存数据
        courseSituationResultCacheCount.put(userAppletBaseVO, userAppletReultVO);
        return userAppletReultVO;
    }


}
