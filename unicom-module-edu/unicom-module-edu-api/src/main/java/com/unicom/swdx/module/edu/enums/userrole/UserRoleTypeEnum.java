package com.unicom.swdx.module.edu.enums.userrole;


import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 课程类型枚举
 * @date 2024-10-11
 */
@Getter
public enum UserRoleTypeEnum {

    /**
     * 培训管理员角色
     */
    PEI_XUN(208L, "培训管理员"),

    /**
     * 教务管理员角色
     */
    JIAO_WU(187L, "教务管理员"),

    /**
     * 班主任角色
     */
    BAN_ZHU_REN(211L, "班主任"),

    /**
     * 超级管理员角色
     */
    SUPER_ADMIN(1L, "超级管理员");

    private final Long type;

    private final String desc;

    UserRoleTypeEnum(Long type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据类型获取描述
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (UserRoleTypeEnum item : UserRoleTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     * @param desc 描述
     * @return 类型值
     */
    public static Long getTypeByDesc(String desc) {
        for (UserRoleTypeEnum item : UserRoleTypeEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
