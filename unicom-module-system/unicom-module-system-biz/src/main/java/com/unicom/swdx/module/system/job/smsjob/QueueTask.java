package com.unicom.swdx.module.system.job.smsjob;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.util.validation.ValidationUtils;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.convert.message.TimedTaskConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.message.TimedTaskMapper;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.OaNoticeMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.service.messagebase.MessageBaseService;
import com.unicom.swdx.module.system.service.sms.SmsLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Executors;

/**
 * 延时发布队列任务
 */
@Component
@Slf4j
public class QueueTask implements ApplicationRunner {
    @Resource
    private MessageBaseService messageBaseService;
    private static final DelayQueue<DelayTask> delayQueue = new DelayQueue<>();
    @Resource
    private TimedTaskMapper timedTaskMapper;
    @Resource
    private OaNoticeMapper oaNoticeMapper;
    @Resource
    private AdminUserMapper userMapper;

    @Resource(name = "taskScheduler")
    private AsyncListenableTaskExecutor taskExecutor;

    @Resource
    private RedisUtil redisUtil;

    private final String msgTaskKey = "message:task:id:";
    /**
     * 加入到延时队列中
     *
     * @param task
     */
    public static void put(DelayTask task) {
        log.info("加入延时任务，{}", task.getData().getPhone());
        delayQueue.put(task);
    }

    /**
     * 稿件任务加入到延时队列中
     */
    public static void putTask(SmsSendReq req) {
        // 毫秒
        log.info("加入延时任务，手机号：{}", req.getPhone());
        delayQueue.put(new DelayTask(req, req.getSendTime().getTime() - System.currentTimeMillis()));
    }

    /**
     * 清除延时队列
     */
    public static void clearQueue() {
        log.info("清除延时队列");
        delayQueue.clear();
    }

    /**
     * 取消延时任务
     *
     * @param task
     * @return
     */
    public static boolean remove(DelayTask task) {
        log.info("取消延时任务：{}", task);
        return delayQueue.remove(task);
    }

    public static boolean remove(String code) {
        log.info("取消延时任务：{}", code);
        SmsSendReq smsSendReq = new SmsSendReq();
        smsSendReq.setTitle(code);
        DelayTask delayTask= new DelayTask(smsSendReq ,0l);

        Iterator<DelayTask>  iterator = delayQueue.iterator();
        while (iterator.hasNext()){
            DelayTask task =  iterator.next();
            if(StrUtil.equals(code ,task.getData().getTitle() )){
                iterator.remove();
            }
        }

        return delayQueue.remove(delayTask);
    }

    public static boolean remove(Long sendId) {
        log.info("取消延时任务：{}", sendId);
        SmsSendReq smsSendReq = new SmsSendReq();
        smsSendReq.setSendId(sendId);
        DelayTask delayTask= new DelayTask(smsSendReq ,0l);

        Iterator<DelayTask>  iterator = delayQueue.iterator();
        while (iterator.hasNext()){
            DelayTask task =  iterator.next();
            if(sendId.equals(task.getData().getSendId())){
                iterator.remove();
            }
        }

        return delayQueue.remove(delayTask);
    }


    public static boolean remove(String code , SmsLogService smsLogService ) {
        log.info("取消延时任务：{}", code);
        SmsSendReq smsSendReq = new SmsSendReq();
        smsSendReq.setTitle(code);
        DelayTask delayTask= new DelayTask(smsSendReq ,0l);

        Iterator<DelayTask>  iterator = delayQueue.iterator();
        while (iterator.hasNext()){
            DelayTask task =  iterator.next();
            if(StrUtil.equals(code ,task.getData().getTitle() )){
                smsLogService.createSmsLog("取消发送短信code", null, task.getData().getTitle() , null);
                iterator.remove();
            }
        }

        return delayQueue.remove(delayTask);
    }

    public static boolean remove(Long sendId , SmsLogService smsLogService ) {
        log.info("取消延时任务：{}", sendId);
        SmsSendReq smsSendReq = new SmsSendReq();
        smsSendReq.setSendId(sendId);
        DelayTask delayTask= new DelayTask(smsSendReq ,0l);

        Iterator<DelayTask>  iterator = delayQueue.iterator();
        while (iterator.hasNext()){
            DelayTask task =  iterator.next();
            if(sendId.equals(task.getData().getSendId())){
                smsLogService.createSmsLog("取消发送短信code", null, task.getData().getTitle() , null);
                iterator.remove();
            }
        }

        return delayQueue.remove(delayTask);
    }


    @Override
    public void run(ApplicationArguments args) {
        log.info("初始化延时队列");
        Executors.newSingleThreadExecutor().execute(new Thread(this::executeThread));
//        initQueueTask();
        log.info("队列任务数量：" + delayQueue.size());
    }

    /**
     * 初始化延时队列
     */
    public void initQueueTask() {
        // TODO 读取未执行的延时任务，业务逻辑处理

        List<TimedTaskDO> timedTaskDO =  timedTaskMapper.selectByStatus();
        for (TimedTaskDO taskDO : timedTaskDO) {
            SmsSendReq req = TimedTaskConvert.INSTANCE.convert1(taskDO);
            req.setTaskId(taskDO.getId());
            if(req.getPhone()==null){
                return;
            }
            req.setPhone(taskDO.getPhone());
            if (req.getSendTime()==null){
                return;
            }
            putTask(req);
        }
    }

    /**
     * 延时任务执行线程
     */
    private void executeThread() {
        while (true) {
            try {
                DelayTask task = delayQueue.take();
                taskExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        processTask(task);
                    }
                });
                log.info("待执行任务队列数量：{}", delayQueue.size());

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("当前线程执行异常，{}", e.getMessage());
            }

            if(delayQueue.size()==0){
                // 在没有可处理元素时休眠一段时间，以减少CPU消耗
                try {
                    Thread.sleep(10000); // 休眠10秒钟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    // 处理中断异常
                }
            }


        }
    }

    /**
     * 队列中未执行任务数量
     *
     * @return
     */
    public static int getQueueSize() {
        return delayQueue.size();
    }

    /**
     * 内部执行发送短信队列
     *
     * @param task
     */
    private void processTask(DelayTask task) {

        boolean skip = false;
        if( System.currentTimeMillis() -task.getExpireTime() > 60*1000*60){  //超过1小时的数据不再处理直接丢弃
            log.info("短信延时任务 超过1小时的数据不再处理直接丢弃：跳过执行 任务id ={}" ,  task.getData().getTaskId());
            skip =true;
        }


        // 防止多实例并发问题，这里用redis分布式锁，并设置过期时间为2分钟
        Object value = redisUtil.get(msgTaskKey + task.getData().getTaskId());
        if (Objects.nonNull(value)) {
            // 如果有值，说明有其他线程在执行
            log.info("短信延时任务进程冲突，其他进程正在执行：跳过执行 任务id ={}" ,  task.getData().getTaskId());
            return;
        }
        // 如果没有值，则先加锁
        Boolean result = redisUtil.setIfAbsent(msgTaskKey + task.getData().getTaskId(),true,240);
        if(!result){
            return;
        }
        log.info("执行延时任务：发送任务id：{}", task.getData().getTaskId());
        if(1==task.getData().getType()){
//            messageBaseService.sendSingleMessage(task.getData().getPhone().toString(),task.getData().getMessage());
            TimedTaskDO timedTaskDO = TimedTaskConvert.INSTANCE.convert(task.getData());
            timedTaskDO.setPhone(task.getData().getPhone());
            if(timedTaskDO==null|| CollectionUtil.isEmpty(timedTaskDO.getPhone())){
                return;
            }
            String mobile= (timedTaskDO.getPhone()).get(0);

            if(skip == false && ValidationUtils.isMobile(mobile)){
                messageBaseService.sendSingleMessage(mobile,"【湖南省委党校】"+task.getData().getMessage());
                timedTaskDO.setSuccessMessage(true);
            }else{
                timedTaskDO.setFailMessage(true);
            }
            AdminUserDO user =userMapper.selectByMobile(mobile);
            if(user!=null){
                timedTaskDO.setReceiver(user.getNickname());
            }
            timedTaskDO.setId(task.getData().getTaskId());
            timedTaskDO.setStatus(true);
            timedTaskMapper.updateById(timedTaskDO);
//            updateTask(task);
        }
        //发送通知
        if(2==task.getData().getType()){

            TimedTaskDO timedTaskDO = TimedTaskConvert.INSTANCE.convert(task.getData());
            OaNoticeDO notice = new OaNoticeDO();
            notice.setContent(timedTaskDO.getMessage());
            notice.setTitle(timedTaskDO.getTitle());
            int countFail = 0;
            StringBuilder sb0 = new StringBuilder();
            List <String> mobileNumbers = (timedTaskDO.getPhone());
            sb0.append("|");
            for(String mobile:mobileNumbers){
                AdminUserDO user =userMapper.selectByMobile(mobile);
                if(user==null){
                    countFail++;
                    continue;
                }
                sb0.append(user.getId()).append("|");
            }
            notice.setRecipientUserId(sb0.toString());
            notice.setType(4);
            notice.setStatus(1);

            if(skip ==false){
                oaNoticeMapper.insert(notice);
            }

            timedTaskDO.setStatus(true);
            timedTaskDO.setSuccessMessage(true);
//            timedTaskDO.setSuccessNotice(mobileNumbers.size()-countFail);
//            timedTaskDO.setFailNotice(countFail);
            timedTaskDO.setId(task.getData().getTaskId());
            timedTaskMapper.updateById(timedTaskDO);
//            updateTask(task);

        }

    }

    @Transactional
    public void updateTask(DelayTask task) {
        TimedTaskDO timedTaskDO = TimedTaskConvert.INSTANCE.convert(task.getData());
        timedTaskDO.setPhone(task.getData().getPhone());
        if(timedTaskDO==null|| CollectionUtil.isEmpty(timedTaskDO.getPhone())){
            return;
        }
        String mobile= (timedTaskDO.getPhone()).get(0);
        timedTaskDO.setSuccessMessage(true);
        timedTaskDO.setId(task.getData().getTaskId());
        timedTaskDO.setStatus(true);
        AdminUserDO user =userMapper.selectByMobile(mobile);
        if(user!=null){
            timedTaskDO.setReceiver(user.getNickname());
        }
        timedTaskMapper.updateById(timedTaskDO);
    }
}
