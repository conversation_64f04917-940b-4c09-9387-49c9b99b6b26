ALTER TABLE "middle_edu"."account_api_limit" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".account_api_limit_id_seq1'::regclass);

ALTER TABLE "middle_edu"."account_api_limit" RENAME CONSTRAINT "cons134222334_fbc8d620" TO "cons134222334_fbc8d620_3C0AFC10";

ALTER TABLE "middle_edu"."act_evt_log" RENAME CONSTRAINT "primary_60e54fa5_98B45912_397C8A7F" TO "primary_60e54fa5_98B45912_397C8A7F_14EB8C7E";

ALTER TABLE "middle_edu"."act_ge_bytearray" DROP CONSTRAINT "act_fk_bytearr_depl_e0b9fb9f_386684AD_1C02C098";

ALTER TABLE "middle_edu"."act_ge_bytearray" RENAME CONSTRAINT "primary_ff02eaa7_5AE50034_936713FE" TO "primary_ff02eaa7_5AE50034_936713FE_BA7D10B5";

ALTER TABLE "middle_edu"."act_ge_property" RENAME CONSTRAINT "primary_9b8999eb_4234869D_1CFFF58A" TO "primary_9b8999eb_4234869D_1CFFF58A_6D46031B";

ALTER TABLE "middle_edu"."act_hi_actinst" RENAME CONSTRAINT "primary_972b483d_1236706F_7D4AC913" TO "primary_972b483d_1236706F_7D4AC913_3EBE060A";

ALTER TABLE "middle_edu"."act_hi_attachment" RENAME CONSTRAINT "primary_fbe5b030_F9339888_83720535" TO "primary_fbe5b030_F9339888_83720535_4425177D";

ALTER TABLE "middle_edu"."act_hi_comment" RENAME CONSTRAINT "primary_6538de96_F9E61577_B1073290" TO "primary_6538de96_F9E61577_B1073290_1162D773";

ALTER TABLE "middle_edu"."act_hi_detail" RENAME CONSTRAINT "primary_82645a42_BA6A78AB_837EFFD8" TO "primary_82645a42_BA6A78AB_837EFFD8_5A65179A";

ALTER TABLE "middle_edu"."act_hi_entitylink" RENAME CONSTRAINT "primary_3b187a36_D52478F6_F5BC3B5" TO "primary_3b187a36_D52478F6_F5BC3B5_34D1645E";

ALTER TABLE "middle_edu"."act_hi_identitylink" RENAME CONSTRAINT "primary_7207887b_937F9547_4593E9A0" TO "primary_7207887b_937F9547_4593E9A0_FF3F8C51";

ALTER TABLE "middle_edu"."act_hi_procinst" DROP CONSTRAINT "proc_inst_id__76709b9a_315DB432_B113D121";

ALTER TABLE "middle_edu"."act_hi_procinst" RENAME CONSTRAINT "primary_6f119797_2377EC_8102CDE4" TO "primary_6f119797_2377EC_8102CDE4_59F34CC0";

ALTER TABLE "middle_edu"."act_hi_taskinst" RENAME CONSTRAINT "primary_20ac88c8_FB5CB9B2_AC42034C" TO "primary_20ac88c8_FB5CB9B2_AC42034C_28160831";

ALTER TABLE "middle_edu"."act_hi_tsk_log" RENAME CONSTRAINT "primary_477e5104_2A13309A_8347E9F" TO "primary_477e5104_2A13309A_8347E9F_A3B24CA";

ALTER TABLE "middle_edu"."act_hi_varinst" RENAME CONSTRAINT "primary_a1dd58c8_A931B67F_B92B3097" TO "primary_a1dd58c8_A931B67F_B92B3097_204AEBB6";

ALTER TABLE "middle_edu"."act_id_bytearray" RENAME CONSTRAINT "primary_60d1ac4a_F128182F_767F9374" TO "primary_60d1ac4a_F128182F_767F9374_F66AE5D6";

ALTER TABLE "middle_edu"."act_id_group" RENAME CONSTRAINT "primary_4e1eb95c_9CC0DC9_A942A645" TO "primary_4e1eb95c_9CC0DC9_A942A645_A22C319E";

ALTER TABLE "middle_edu"."act_id_info" RENAME CONSTRAINT "primary_f3679fdf_80ABA9F3_879F757E" TO "primary_f3679fdf_80ABA9F3_879F757E_6A180039";

ALTER TABLE "middle_edu"."act_id_membership" DROP CONSTRAINT "act_fk_memb_user_f0c7a1d9_6ECF32F7_BACA6358";

ALTER TABLE "middle_edu"."act_id_membership" DROP CONSTRAINT "act_fk_memb_group_57a33435_152C612F_B1556C8";

ALTER TABLE "middle_edu"."act_id_membership" DROP CONSTRAINT "primary_8add9498_630BCDFA_45735A0";

ALTER TABLE "middle_edu"."act_id_membership" ADD CONSTRAINT "primary_8add9498_630BCDFA_45735A0_81101928" PRIMARY KEY ("user_id_", "group_id_");

ALTER TABLE "middle_edu"."act_id_priv" DROP CONSTRAINT "act_uniq_priv_name_61717499_86FA514A_67AFD545";

ALTER TABLE "middle_edu"."act_id_priv" RENAME CONSTRAINT "primary_b31a03fe_E341C96E_DB217C51" TO "primary_b31a03fe_E341C96E_DB217C51_A33F44D9";

ALTER TABLE "middle_edu"."act_id_priv_mapping" DROP CONSTRAINT "act_fk_priv_mapping_72e268dc_B2EB2522_6F54B6A3";

ALTER TABLE "middle_edu"."act_id_priv_mapping" RENAME CONSTRAINT "primary_542c4a6f_CF793173_D32BBCBD" TO "primary_542c4a6f_CF793173_D32BBCBD_3A98E210";

ALTER TABLE "middle_edu"."act_id_property" RENAME CONSTRAINT "primary_73930ca8_B0805847_2E00A41C" TO "primary_73930ca8_B0805847_2E00A41C_A55ED9DB";

ALTER TABLE "middle_edu"."act_id_token" RENAME CONSTRAINT "primary_facd1922_B0B7EAA9_42627BEF" TO "primary_facd1922_B0B7EAA9_42627BEF_C73079DA";

ALTER TABLE "middle_edu"."act_id_user" RENAME CONSTRAINT "primary_90c79662_3A2349A9_699F529A" TO "primary_90c79662_3A2349A9_699F529A_FC01C640";

ALTER TABLE "middle_edu"."act_procdef_info" DROP CONSTRAINT "act_uniq_info_procdef_147070f4_B532537E_7F94524D";

ALTER TABLE "middle_edu"."act_procdef_info" DROP CONSTRAINT "act_fk_info_procdef_3c51449f_5847D9E9_D8F65E95";

ALTER TABLE "middle_edu"."act_procdef_info" DROP CONSTRAINT "act_fk_info_json_ba_70978839_DAA7D019_79726E4F";

ALTER TABLE "middle_edu"."act_procdef_info" RENAME CONSTRAINT "primary_d2c07a59_5D95C4CD_A5065925" TO "primary_d2c07a59_5D95C4CD_A5065925_5B9CFD49";

ALTER TABLE "middle_edu"."act_re_deployment" RENAME CONSTRAINT "primary_1a20a900_90C14C29_D080FCB" TO "primary_1a20a900_90C14C29_D080FCB_5951C709";

ALTER TABLE "middle_edu"."act_re_model" DROP CONSTRAINT "act_fk_model_source_extra_2146ded4_AC77E857_754150F";

ALTER TABLE "middle_edu"."act_re_model" DROP CONSTRAINT "act_fk_model_source_838c0cb4_7401201C_E5AD66F1";

ALTER TABLE "middle_edu"."act_re_model" DROP CONSTRAINT "act_fk_model_deployment_46d78a44_4FF0419B_75AC3E08";

ALTER TABLE "middle_edu"."act_re_model" RENAME CONSTRAINT "primary_1048a1da_662BD565_9C1A7BEA" TO "primary_1048a1da_662BD565_9C1A7BEA_5A50DD84";

ALTER TABLE "middle_edu"."act_re_procdef" DROP CONSTRAINT "act_uniq_procdef_109b8f49_300136CB_3CFD5FE6";

ALTER TABLE "middle_edu"."act_re_procdef" RENAME CONSTRAINT "primary_f21f8114_846F9F9E_4458CF8C" TO "primary_f21f8114_846F9F9E_4458CF8C_CF9E6E72";

ALTER TABLE "middle_edu"."act_ru_actinst" RENAME CONSTRAINT "primary_ea85d53b_6E09A25B_E4D600DE" TO "primary_ea85d53b_6E09A25B_E4D600DE_1BA38F8F";

ALTER TABLE "middle_edu"."act_ru_deadletter_job" DROP CONSTRAINT "act_fk_deadletter_job_process_instance_55ef79c8_69A1E811_144212";

ALTER TABLE "middle_edu"."act_ru_deadletter_job" DROP CONSTRAINT "act_fk_deadletter_job_proc_def_17a5ac76_5BD970F4_34E87864";

ALTER TABLE "middle_edu"."act_ru_deadletter_job" DROP CONSTRAINT "act_fk_deadletter_job_execution_896fd650_79D3CD5F_2A34F4EB";

ALTER TABLE "middle_edu"."act_ru_deadletter_job" DROP CONSTRAINT "act_fk_deadletter_job_exception_7aa88e19_9923873E_CC43221D";

ALTER TABLE "middle_edu"."act_ru_deadletter_job" DROP CONSTRAINT "act_fk_deadletter_job_custom_values_1f92c0d0_B152ADC6_8F3F6855";

ALTER TABLE "middle_edu"."act_ru_deadletter_job" RENAME CONSTRAINT "primary_4a02464d_446E2E1_CA4ADB91" TO "primary_4a02464d_446E2E1_CA4ADB91_D6D3A5D";

ALTER TABLE "middle_edu"."act_ru_entitylink" RENAME CONSTRAINT "primary_3195e478_F8DABD2D_1C0E993F" TO "primary_3195e478_F8DABD2D_1C0E993F_887E9CB6";

ALTER TABLE "middle_edu"."act_ru_event_subscr" DROP CONSTRAINT "act_fk_event_exec_57d836dc_42CDE979_53F3C39F";

ALTER TABLE "middle_edu"."act_ru_event_subscr" RENAME CONSTRAINT "primary_6423e48e_EAC2BF86_87E92ADC" TO "primary_6423e48e_EAC2BF86_87E92ADC_64B60412";

ALTER TABLE "middle_edu"."act_ru_execution" DROP CONSTRAINT "act_fk_exe_super_ce768dec_871141F0_BE5308F1";

ALTER TABLE "middle_edu"."act_ru_execution" DROP CONSTRAINT "act_fk_exe_procinst_5ac084ed_46E69EDA_50D098E5";

ALTER TABLE "middle_edu"."act_ru_execution" DROP CONSTRAINT "act_fk_exe_procdef_b8298733_FE17B1F8_1DC93C3D";

ALTER TABLE "middle_edu"."act_ru_execution" DROP CONSTRAINT "act_fk_exe_parent_a3adc644_3A0CF40F_9B0B9161";

ALTER TABLE "middle_edu"."act_ru_execution" RENAME CONSTRAINT "primary_3f63e43b_83069E6_B3EA3ABC" TO "primary_3f63e43b_83069E6_B3EA3ABC_ABBBF2B7";

ALTER TABLE "middle_edu"."act_ru_external_job" DROP CONSTRAINT "act_fk_external_job_exception_dabf98d7_6BABE43_26700F0B";

ALTER TABLE "middle_edu"."act_ru_external_job" DROP CONSTRAINT "act_fk_external_job_custom_values_d612140e_A317C224_C8508E10";

ALTER TABLE "middle_edu"."act_ru_external_job" RENAME CONSTRAINT "primary_7db84eec_CE87171_B978B4C6" TO "primary_7db84eec_CE87171_B978B4C6_C22660EC";

ALTER TABLE "middle_edu"."act_ru_history_job" RENAME CONSTRAINT "primary_e87df961_FF388220_86E6FAD2" TO "primary_e87df961_FF388220_86E6FAD2_F5948D25";

ALTER TABLE "middle_edu"."act_ru_identitylink" DROP CONSTRAINT "act_fk_tskass_task_3fa52fcb_C56F71D0_ECBA16F9";

ALTER TABLE "middle_edu"."act_ru_identitylink" DROP CONSTRAINT "act_fk_idl_procinst_fedfb58e_9474F6EC_75D60A09";

ALTER TABLE "middle_edu"."act_ru_identitylink" DROP CONSTRAINT "act_fk_athrz_procedef_9ca8f195_E01AC02_2264D7AD";

ALTER TABLE "middle_edu"."act_ru_identitylink" RENAME CONSTRAINT "primary_bed36a3d_50480084_F0267E30" TO "primary_bed36a3d_50480084_F0267E30_98FC7466";

ALTER TABLE "middle_edu"."act_ru_job" DROP CONSTRAINT "act_fk_job_process_instance_d8fe6294_3087DDD7_2F56C139";

ALTER TABLE "middle_edu"."act_ru_job" DROP CONSTRAINT "act_fk_job_proc_def_f757ad42_DCD4CA21_E74777BA";

ALTER TABLE "middle_edu"."act_ru_job" DROP CONSTRAINT "act_fk_job_execution_44a4d41c_85EEB33C_160008B3";

ALTER TABLE "middle_edu"."act_ru_job" DROP CONSTRAINT "act_fk_job_exception_aee302e5_5E62C4C4_3C2819B";

ALTER TABLE "middle_edu"."act_ru_job" DROP CONSTRAINT "act_fk_job_custom_values_2f03329c_8F6161_77E8A658";

ALTER TABLE "middle_edu"."act_ru_job" RENAME CONSTRAINT "primary_ba02dd76_52FC11B9_8155B3C5" TO "primary_ba02dd76_52FC11B9_8155B3C5_BCB1EDEE";

ALTER TABLE "middle_edu"."act_ru_suspended_job" DROP CONSTRAINT "act_fk_suspended_job_process_instance_5c5ad8d4_98D969EB_52BD1EC";

ALTER TABLE "middle_edu"."act_ru_suspended_job" DROP CONSTRAINT "act_fk_suspended_job_proc_def_34e8a382_5A7F788D_5BD335CA";

ALTER TABLE "middle_edu"."act_ru_suspended_job" DROP CONSTRAINT "act_fk_suspended_job_execution_61d13a5c_CFC05A0D_90A9CA50";

ALTER TABLE "middle_edu"."act_ru_suspended_job" DROP CONSTRAINT "act_fk_suspended_job_exception_3039b925_2C4AD08B_C7A52A2E";

ALTER TABLE "middle_edu"."act_ru_suspended_job" DROP CONSTRAINT "act_fk_suspended_job_custom_values_b58558dc_4376800F_566A8402";

ALTER TABLE "middle_edu"."act_ru_suspended_job" RENAME CONSTRAINT "primary_dd9e3eba_7A1426F8_ABBDE514" TO "primary_dd9e3eba_7A1426F8_ABBDE514_43F4E085";

ALTER TABLE "middle_edu"."act_ru_task" DROP CONSTRAINT "act_fk_task_procinst_33590119_FAD2E475_AE43EF90";

ALTER TABLE "middle_edu"."act_ru_task" DROP CONSTRAINT "act_fk_task_procdef_b2cb1e5f_C4CC2724_2881CD83";

ALTER TABLE "middle_edu"."act_ru_task" DROP CONSTRAINT "act_fk_task_exe_e015bf9c_C6CDC7B4_A1FAB47F";

ALTER TABLE "middle_edu"."act_ru_task" RENAME CONSTRAINT "primary_e37aeb10_27115EB_FA10E89D" TO "primary_e37aeb10_27115EB_FA10E89D_124E089A";

ALTER TABLE "middle_edu"."act_ru_timer_job" DROP CONSTRAINT "act_fk_timer_job_process_instance_d8a0ac14_B8B28F5_79BF7B77";

ALTER TABLE "middle_edu"."act_ru_timer_job" DROP CONSTRAINT "act_fk_timer_job_proc_def_9bbcf6c2_D3691F16_48C00325";

ALTER TABLE "middle_edu"."act_ru_timer_job" DROP CONSTRAINT "act_fk_timer_job_execution_64ddbd9c_D0AB0767_ECF75B1";

ALTER TABLE "middle_edu"."act_ru_timer_job" DROP CONSTRAINT "act_fk_timer_job_exception_c426cc65_AF7C26F4_27D0E796";

ALTER TABLE "middle_edu"."act_ru_timer_job" DROP CONSTRAINT "act_fk_timer_job_custom_values_27a9c1c_867C1F63_FB2149BE";

ALTER TABLE "middle_edu"."act_ru_timer_job" RENAME CONSTRAINT "primary_b8878310_5310CE50_5627B365" TO "primary_b8878310_5310CE50_5627B365_34FD7E54";

ALTER TABLE "middle_edu"."act_ru_variable" DROP CONSTRAINT "act_fk_var_procinst_662a3c40_4D2D300_74A9C2AE";

ALTER TABLE "middle_edu"."act_ru_variable" DROP CONSTRAINT "act_fk_var_exe_a6d7e815_307F31B9_9F337F13";

ALTER TABLE "middle_edu"."act_ru_variable" DROP CONSTRAINT "act_fk_var_bytearray_3259976f_836F6E3F_9180A733";

ALTER TABLE "middle_edu"."act_ru_variable" RENAME CONSTRAINT "primary_85348279_F2045C5C_B6FC8EC7" TO "primary_85348279_F2045C5C_B6FC8EC7_4045AD76";

ALTER TABLE "middle_edu"."api_account" ALTER COLUMN "account_id" SET DEFAULT nextval('"middle_edu".api_account_account_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_account" RENAME CONSTRAINT "cons134222179_864a37f5" TO "cons134222179_864a37f5_CC29F3BC";

ALTER TABLE "middle_edu"."api_application" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".api_application_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_application" RENAME CONSTRAINT "cons134222337_6d1ecea" TO "cons134222337_6d1ecea_8430CB28";

ALTER TABLE "middle_edu"."api_group" ALTER COLUMN "api_group_id" SET DEFAULT nextval('"middle_edu".api_group_api_group_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_group" RENAME CONSTRAINT "cons134222113_657eeb5a" TO "cons134222113_657eeb5a_354ED015";

ALTER TABLE "middle_edu"."api_request_record" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".api_request_record_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_request_record" RENAME CONSTRAINT "cons134222181_f06daf58" TO "cons134222181_f06daf58_A2431677";

ALTER TABLE "middle_edu"."api_server" ALTER COLUMN "server_id" SET DEFAULT nextval('"middle_edu".api_server_server_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_server_group" ALTER COLUMN "server_group_id" SET DEFAULT nextval('"middle_edu".api_server_group_server_group_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_server_group" RENAME CONSTRAINT "cons134222183_a6f29bd6" TO "cons134222183_a6f29bd6_D21BDDC3";

ALTER TABLE "middle_edu"."api_url" ALTER COLUMN "api_id" SET DEFAULT nextval('"middle_edu".api_url_api_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_url" RENAME CONSTRAINT "cons134222184_2090c444" TO "cons134222184_2090c444_7E94B37B";

ALTER TABLE "middle_edu"."api_url_param" ALTER COLUMN "param_id" SET DEFAULT nextval('"middle_edu".api_url_param_param_id_seq1'::regclass);

ALTER TABLE "middle_edu"."api_url_param" RENAME CONSTRAINT "cons134222185_1e2f1520" TO "cons134222185_1e2f1520_D5C9535D";

ALTER TABLE "middle_edu"."bpm_form" RENAME CONSTRAINT "cons134219311_6f9a03d7_6E2D5C06_3469E7DE_A1C0B268" TO "cons134219311_6f9a03d7_6E2D5C06_3469E7DE_A1C0B268_F309C876";

ALTER TABLE "middle_edu"."bpm_process_definition_ext" RENAME CONSTRAINT "cons134219313_e7089f5a_6C86E2E8_BB082612_B36DA2EA" TO "cons134219313_e7089f5a_6C86E2E8_BB082612_B36DA2EA_287C2411";

ALTER TABLE "middle_edu"."bpm_process_instance_ext" DROP CONSTRAINT "cons134219689_c2c3d3d7_F624981D_1A03814E_9BE9524";

ALTER TABLE "middle_edu"."bpm_process_instance_ext" RENAME CONSTRAINT "cons134219686_16f4ed3b_45D3BBB5_E8C3541D_10B3B347" TO "cons134219686_16f4ed3b_45D3BBB5_E8C3541D_10B3B347_2FB5AC";

ALTER TABLE "middle_edu"."bpm_task_assign_rule" RENAME CONSTRAINT "cons134219315_2e0a0929_4729051A_F2B49734_25EA3738" TO "cons134219315_2e0a0929_4729051A_F2B49734_25EA3738_C983D23F";

ALTER TABLE "middle_edu"."bpm_task_ext" RENAME CONSTRAINT "cons134219687_e326d53c_1DB09C26_EF019BB8_498440D8" TO "cons134219687_e326d53c_1DB09C26_EF019BB8_498440D8_1A02B4E8";

ALTER TABLE "middle_edu"."bpm_user_group" ALTER COLUMN "name" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "name"::varchar(255);

ALTER TABLE "middle_edu"."bpm_user_group" ALTER COLUMN "description" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "description"::varchar(255);

ALTER TABLE "middle_edu"."bpm_user_group" ALTER COLUMN "member_user_ids" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "member_user_ids"::varchar(255);

ALTER TABLE "middle_edu"."bpm_user_group" ALTER COLUMN "creator" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "creator"::varchar(255);

ALTER TABLE "middle_edu"."bpm_user_group" ALTER COLUMN "updater" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "updater"::varchar(255);

ALTER TABLE "middle_edu"."bpm_user_group" RENAME CONSTRAINT "cons134219317_e6e012c4_53BCA079_154912C6_67370C66" TO "cons134219317_e6e012c4_53BCA079_154912C6_67370C66_DD47B4E1";

ALTER TABLE "middle_edu"."dzbp_course" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".dzbp_course_id_seq1'::regclass);

ALTER TABLE "middle_edu"."dzbp_course" ALTER COLUMN "attendance_rate" TYPE text COLLATE "pg_catalog"."default" USING "attendance_rate"::text;

ALTER TABLE "middle_edu"."dzbp_course_stu_att" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".dzbp_course_stu_att_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_cadre_information_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "sex" TYPE text COLLATE "pg_catalog"."default" USING "sex"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "card_no" TYPE text COLLATE "pg_catalog"."default" USING "card_no"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "position" TYPE text COLLATE "pg_catalog"."default" USING "position"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "graduation_school" TYPE text COLLATE "pg_catalog"."default" USING "graduation_school"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "remark" TYPE text COLLATE "pg_catalog"."default" USING "remark"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_cadre_information" ALTER COLUMN "photo" TYPE text COLLATE "pg_catalog"."default" USING "photo"::text;

COMMENT ON COLUMN "middle_edu"."edu_cadre_information"."last_training_class" IS '最近培训班次';

ALTER TABLE "middle_edu"."edu_cadre_information" RENAME CONSTRAINT "con_middle_edu_cadre_information_constraint_1" TO "con_middle_edu_cadre_information_constraint_1_3F8C9154";

ALTER TABLE "middle_edu"."edu_class_clock_calendar" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_clock_calendar_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_class_clock_calendar" ALTER COLUMN "clock_date" TYPE timestamp(0) USING "clock_date"::timestamp(0);

ALTER TABLE "middle_edu"."edu_class_clock_calendar" RENAME CONSTRAINT "con_middle_edu_class_clock_calendar_constraint_1" TO "con_middle_edu_class_clock_calendar_constraint_1_DF0A45DA";

ALTER TABLE "middle_edu"."edu_class_clock_in" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_clock_in_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_class_clock_in" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_class_clock_in" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_class_clock_in" RENAME CONSTRAINT "con_middle_edu_class_clock_in_constraint_1" TO "con_middle_edu_class_clock_in_constraint_1_F5DDD5CC";

ALTER TABLE "middle_edu"."edu_class_committee" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_committee_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_class_committee" ALTER COLUMN "class_committee_name" TYPE text COLLATE "pg_catalog"."default" USING "class_committee_name"::text;

ALTER TABLE "middle_edu"."edu_class_committee" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_class_committee" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_class_committee" RENAME CONSTRAINT "con_middle_edu_class_committee_constraint_1" TO "con_middle_edu_class_committee_constraint_1_98E72214";

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_completion_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "trainee_name" TYPE text COLLATE "pg_catalog"."default" USING "trainee_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "group_name" TYPE text COLLATE "pg_catalog"."default" USING "group_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "unit_position" TYPE text COLLATE "pg_catalog"."default" USING "unit_position"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "class_position" TYPE text COLLATE "pg_catalog"."default" USING "class_position"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "assessment_name" TYPE text COLLATE "pg_catalog"."default" USING "assessment_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "serial_number" TYPE text COLLATE "pg_catalog"."default" USING "serial_number"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_class_completion" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_class_completion" RENAME CONSTRAINT "con_middle_edu_class_completion_constraint_1" TO "con_middle_edu_class_completion_constraint_1_674D9880";

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_completion_template_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "serial_number" TYPE text COLLATE "pg_catalog"."default" USING "serial_number"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "column_name" TYPE text COLLATE "pg_catalog"."default" USING "column_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "conversion_announcement" TYPE text COLLATE "pg_catalog"."default" USING "conversion_announcement"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "assessment_name" TYPE text COLLATE "pg_catalog"."default" USING "assessment_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "template_name" TYPE text COLLATE "pg_catalog"."default" USING "template_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "module_name" TYPE text COLLATE "pg_catalog"."default" USING "module_name"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" ALTER COLUMN "id_code" TYPE text COLLATE "pg_catalog"."default" USING "id_code"::text;

ALTER TABLE "middle_edu"."edu_class_completion_template" RENAME CONSTRAINT "con_middle_edu_class_completion_template_constraint_1" TO "con_middle_edu_class_completion_template_constraint_1_840C6C54";

ALTER TABLE "middle_edu"."edu_class_course" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_course_id_seq1'::regclass);

COMMENT ON COLUMN "middle_edu"."edu_class_course"."is_leader_lecture" IS '是否党政领导讲课 0否1是';

COMMENT ON COLUMN "middle_edu"."edu_class_course"."change_type" IS '调课类型';

COMMENT ON COLUMN "middle_edu"."edu_class_course"."is_department_leader" IS '是否厅级领导授课';

ALTER TABLE "middle_edu"."edu_class_course" RENAME CONSTRAINT "edu_class_course_copy1_pkey" TO "edu_class_course_pkey_B87889C7";

ALTER TABLE "middle_edu"."edu_class_course_order" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_course_order_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_class_course_order" ALTER COLUMN "class_id_order" TYPE text COLLATE "pg_catalog"."default" USING "class_id_order"::text;

ALTER TABLE "middle_edu"."edu_class_course_order" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_class_course_order" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

COMMENT ON TABLE "middle_edu"."edu_class_course_teacher" IS '';

ALTER TABLE "middle_edu"."edu_class_course_teacher" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_course_teacher_id_seq1'::regclass);

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."class_course_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."teacher_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."sort" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."creator" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."create_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."updater" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."update_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."deleted" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."tenant_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."dept_name" IS '';

COMMENT ON COLUMN "middle_edu"."edu_class_course_teacher"."course_id" IS '';

ALTER TABLE "middle_edu"."edu_class_course_teacher" RENAME CONSTRAINT "edu_class_course_teacher_pkey" TO "edu_class_course_teacher_pkey1";

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_class_management_id_seq'::regclass);

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "class_name_code" TYPE text COLLATE "pg_catalog"."default" USING "class_name_code"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "class_name" TYPE text COLLATE "pg_catalog"."default" USING "class_name"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "training_object" TYPE text COLLATE "pg_catalog"."default" USING "training_object"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "turn" TYPE text COLLATE "pg_catalog"."default" USING "turn"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "reporting_time" TYPE timestamp(0) USING "reporting_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "class_open_time" TYPE timestamp(0) USING "class_open_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "completion_time" TYPE timestamp(0) USING "completion_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "registration_start_time" TYPE timestamp(0) USING "registration_start_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "accessory" TYPE text COLLATE "pg_catalog"."default" USING "accessory"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "remark" TYPE text COLLATE "pg_catalog"."default" USING "remark"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "registration_end_time" TYPE timestamp(0) USING "registration_end_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "coach_teacher" TYPE text COLLATE "pg_catalog"."default" USING "coach_teacher"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "id_code" TYPE text COLLATE "pg_catalog"."default" USING "id_code"::text;

ALTER TABLE "middle_edu"."edu_class_management" ALTER COLUMN "report_period" TYPE varchar(255) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "middle_edu"."edu_class_management"."report_period" IS '报到午别  1上午 2下午';

ALTER TABLE "middle_edu"."edu_class_management" ADD COLUMN "learning_system_unit" int4 DEFAULT 1;

COMMENT ON COLUMN "middle_edu"."edu_class_management"."learning_system_unit" IS '学制单位，1-天，2-周，3-月，默认为1';

ALTER TABLE "middle_edu"."edu_class_management" RENAME CONSTRAINT "con_middle_edu_shift_management_constraint_1" TO "con_middle_edu_shift_management_constraint_1_4CD4937C";

ALTER TABLE "middle_edu"."edu_classroom_library" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_classroom_library_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_classroom_library" ALTER COLUMN "class_name" TYPE text COLLATE "pg_catalog"."default" USING "class_name"::text;

ALTER TABLE "middle_edu"."edu_classroom_library" ALTER COLUMN "campus_name" TYPE text COLLATE "pg_catalog"."default" USING "campus_name"::text;

ALTER TABLE "middle_edu"."edu_classroom_library" ALTER COLUMN "building_name" TYPE text COLLATE "pg_catalog"."default" USING "building_name"::text;

ALTER TABLE "middle_edu"."edu_classroom_library" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_classroom_library" RENAME CONSTRAINT "con_middle_edu_classroom_library_constraint_1" TO "con_middle_edu_classroom_library_constraint_1_AFC19434";

ALTER TABLE "middle_edu"."edu_clock_in_info" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_clock_in_info_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_clock_in_info" ALTER COLUMN "clocking_time" TYPE timestamp(0) USING "clocking_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_clock_in_info" ALTER COLUMN "check_begin_time" TYPE timestamp(0) USING "check_begin_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_clock_in_info" ALTER COLUMN "check_end_time" TYPE timestamp(0) USING "check_end_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_clock_in_info" ALTER COLUMN "period" TYPE int4 USING "period"::int4;

COMMENT ON COLUMN "middle_edu"."edu_clock_in_info"."period" IS '周期';

ALTER TABLE "middle_edu"."edu_clock_in_info" RENAME CONSTRAINT "con_middle_edu_clock_in_info_constraint_1" TO "con_middle_edu_clock_in_info_constraint_1_982E1134";

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_completion_template_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "serial_number" TYPE text COLLATE "pg_catalog"."default" USING "serial_number"::text;

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "column_name" TYPE text COLLATE "pg_catalog"."default" USING "column_name"::text;

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "conversion_announcement" TYPE text COLLATE "pg_catalog"."default" USING "conversion_announcement"::text;

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "assessment_name" TYPE text COLLATE "pg_catalog"."default" USING "assessment_name"::text;

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "template_name" TYPE text COLLATE "pg_catalog"."default" USING "template_name"::text;

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "module_name" TYPE text COLLATE "pg_catalog"."default" USING "module_name"::text;

ALTER TABLE "middle_edu"."edu_completion_template" ALTER COLUMN "id_code" TYPE text COLLATE "pg_catalog"."default" USING "id_code"::text;

ALTER TABLE "middle_edu"."edu_completion_template" DROP COLUMN "serial_prefix";

ALTER TABLE "middle_edu"."edu_completion_template" DROP COLUMN "serial_number_numeric";

ALTER TABLE "middle_edu"."edu_completion_template" RENAME CONSTRAINT "con_middle_edu_completion_template_constraint_1" TO "con_middle_edu_completion_template_constraint_1_9101A5F4";

ALTER TABLE "middle_edu"."edu_courses" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_courses_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_courses" DROP COLUMN "remark";

ALTER TABLE "middle_edu"."edu_courses" RENAME CONSTRAINT "edu_courses_pkey" TO "edu_courses_pkey_FC1F8B67";

ALTER TABLE "middle_edu"."edu_elective_release" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_elective_release_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_elective_release" RENAME CONSTRAINT "edu_elective_release_pkey" TO "edu_elective_release_pkey_6EBEE467";

ALTER TABLE "middle_edu"."edu_elective_release_classes" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_elective_release_classes_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_elective_release_classes" ALTER COLUMN "creator" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "creator"::varchar(255);

ALTER TABLE "middle_edu"."edu_elective_release_classes" ALTER COLUMN "updater" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "updater"::varchar(255);

ALTER TABLE "middle_edu"."edu_elective_release_classes" RENAME CONSTRAINT "edu_elective_release_classes_pkey" TO "edu_elective_release_classes_pkey_7656F807";

ALTER TABLE "middle_edu"."edu_elective_release_courses" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_elective_release_courses_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_elective_release_courses" RENAME CONSTRAINT "edu_elective_release_courses_pkey" TO "edu_elective_release_courses_pkey_B853EC7";

ALTER TABLE "middle_edu"."edu_elective_trainee_selection" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_elective_trainee_selection_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_elective_trainee_selection" DROP COLUMN "is_distributed";

ALTER TABLE "middle_edu"."edu_elective_trainee_selection" RENAME CONSTRAINT "edu_elective_trainee_selection_pkey" TO "edu_elective_trainee_selection_pkey_CB2B0467";

ALTER TABLE "middle_edu"."edu_evaluation_history" ALTER COLUMN "id" TYPE varchar(64) COLLATE "pg_catalog"."default" USING "id"::varchar(64);

ALTER TABLE "middle_edu"."edu_evaluation_history" ALTER COLUMN "id" DROP DEFAULT;

ALTER TABLE "middle_edu"."edu_evaluation_history" ALTER COLUMN "kc_id" TYPE varchar(64) COLLATE "pg_catalog"."default" USING "kc_id"::varchar(64);

ALTER TABLE "middle_edu"."edu_evaluation_history" ADD COLUMN "teacher_id" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::varchar;

COMMENT ON COLUMN "middle_edu"."edu_evaluation_history"."teacher_id" IS '教师ID';

ALTER TABLE "middle_edu"."edu_evaluation_history" ADD COLUMN "class_id" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::varchar;

COMMENT ON COLUMN "middle_edu"."edu_evaluation_history"."class_id" IS '班级ID';

COMMENT ON TABLE "middle_edu"."edu_frequency" IS '';

ALTER TABLE "middle_edu"."edu_frequency" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_frequency_id_seq1'::regclass);

COMMENT ON COLUMN "middle_edu"."edu_frequency"."id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."user_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."number_of_times" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."update_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."create_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."deleted" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."tenant_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."creator" IS '';

COMMENT ON COLUMN "middle_edu"."edu_frequency"."updater" IS '';

ALTER TABLE "middle_edu"."edu_frequency" RENAME CONSTRAINT "con_middle_edu_frequency_constraint_1" TO "con_middle_edu_frequency_constraint_1_BEBA8394";

ALTER TABLE "middle_edu"."edu_graduation_certificate_numbers" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_graduation_certificate_numbers_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_graduation_certificate_numbers" RENAME CONSTRAINT "edu_graduation_certificate_numbers_pkey" TO "edu_graduation_certificate_numbers_pkey_1B424527";

ALTER TABLE "middle_edu"."edu_holiday" RENAME CONSTRAINT "edu_holiday_pkey" TO "edu_holiday_pkey_D6DDDE49";

COMMENT ON TABLE "middle_edu"."edu_leave_report" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."id" IS '';

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "name" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "name"::varcharbyte;

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."name" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."start_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."end_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."class_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."teacher_id" IS '';

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "creator" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "creator"::varcharbyte;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "create_time" TYPE timestamp(0);

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "create_time" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "create_time" SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "updater" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "updater"::varcharbyte;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "update_time" TYPE timestamp(0);

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "update_time" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "update_time" SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "deleted" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "tenant_id" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report" ALTER COLUMN "tenant_id" SET DEFAULT 0;

COMMENT ON COLUMN "middle_edu"."edu_leave_report"."tenant_id" IS '租户编号';

COMMENT ON TABLE "middle_edu"."edu_leave_report_detail" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."leave_id" IS '';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "leave_status" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "leave_status"::varcharbyte;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."leave_status" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."leave_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."return_time" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."student_id" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."leave_dinner" IS '';

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."return_dinner" IS '';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "destination" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "destination"::varcharbyte;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."destination" IS '';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "cause" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "cause"::varcharbyte;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."cause" IS '';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "creator" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "creator"::varcharbyte;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."creator" IS '创建者';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "create_time" TYPE timestamp(0);

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "create_time" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "create_time" SET DEFAULT CURRENT_TIMESTAMP;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."create_time" IS '创建时间';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "updater" TYPE varcharbyte COLLATE "pg_catalog"."default" USING "updater"::varcharbyte;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."updater" IS '更新者';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "update_time" TYPE timestamp(0);

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "update_time" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "update_time" SET DEFAULT CURRENT_TIMESTAMP;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."update_time" IS '更新时间';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "deleted" SET NOT NULL;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."deleted" IS '是否删除';

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "tenant_id" SET NOT NULL;

ALTER TABLE "middle_edu"."edu_leave_report_detail" ALTER COLUMN "tenant_id" SET DEFAULT 0;

COMMENT ON COLUMN "middle_edu"."edu_leave_report_detail"."tenant_id" IS '租户编号';

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_notice_announcement_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "publisher" TYPE text COLLATE "pg_catalog"."default" USING "publisher"::text;

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "content" TYPE text COLLATE "pg_catalog"."default" USING "content"::text;

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "publish_time" TYPE timestamp(0) USING "publish_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "drafts_time" TYPE timestamp(0) USING "drafts_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "top_time" TYPE timestamp(0) USING "top_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_notice_announcement" ALTER COLUMN "class_ids" TYPE text COLLATE "pg_catalog"."default" USING "class_ids"::text;

COMMENT ON COLUMN "middle_edu"."edu_notice_announcement"."class_ids" IS '通知班级范围';

COMMENT ON COLUMN "middle_edu"."edu_notice_announcement"."publish_id" IS '发布人id';

ALTER TABLE "middle_edu"."edu_notice_announcement" RENAME CONSTRAINT "con_middle_edu_notice_announcement_constraint_1" TO "con_middle_edu_notice_announcement_constraint_1_AF417B94";

ALTER TABLE "middle_edu"."edu_notice_file_url" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_notice_file_url_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_notice_file_url" ALTER COLUMN "url" TYPE text COLLATE "pg_catalog"."default" USING "url"::text;

ALTER TABLE "middle_edu"."edu_notice_file_url" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_notice_file_url" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_notice_file_url" ALTER COLUMN "file_name" TYPE text COLLATE "pg_catalog"."default" USING "file_name"::text;

ALTER TABLE "middle_edu"."edu_notice_file_url" ALTER COLUMN "file_size" TYPE text COLLATE "pg_catalog"."default" USING "file_size"::text;

ALTER TABLE "middle_edu"."edu_notice_file_url" RENAME CONSTRAINT "con_middle_edu_notice_file_url_constraint_1" TO "con_middle_edu_notice_file_url_constraint_1_DF65B4";

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_notification_message_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "publisher" TYPE text COLLATE "pg_catalog"."default" USING "publisher"::text;

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "content" TYPE text COLLATE "pg_catalog"."default" USING "content"::text;

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "publish_time" TYPE timestamp(0) USING "publish_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "drafts_time" TYPE timestamp(0) USING "drafts_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_notification_message" ALTER COLUMN "top_time" TYPE timestamp(0) USING "top_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_notification_message" RENAME CONSTRAINT "con_middle_edu_notification_message_constraint_1" TO "con_middle_edu_notification_message_constraint_1_D824C320";

ALTER TABLE "middle_edu"."edu_notification_message_unit" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_notification_message_unit_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_notification_message_unit" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_notification_message_unit" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_notification_message_unit" RENAME CONSTRAINT "con_middle_edu_notification_message_unit_constraint_1" TO "con_middle_edu_notification_message_unit_constraint_1_FE6C6714";

ALTER TABLE "middle_edu"."edu_plan" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_plan_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_plan" RENAME CONSTRAINT "edu_plan_pkey" TO "edu_plan_pkey_6BD79DE7";

ALTER TABLE "middle_edu"."edu_plan_config" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_plan_config_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_plan_config" RENAME CONSTRAINT "edu_plan_config_pkey" TO "edu_plan_config_pkey_49FDBFA7";

ALTER TABLE "middle_edu"."edu_plan_detail" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_plan_detail_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_plan_detail" RENAME CONSTRAINT "edu_plan_detail_pkey" TO "edu_plan_detail_pkey_A083B449";

ALTER TABLE "middle_edu"."edu_plan_template" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_plan_template_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_plan_template" RENAME CONSTRAINT "edu_plan_template_pkey" TO "edu_plan_template_pkey_9E6F93B7";

ALTER TABLE "middle_edu"."edu_plan_template_config" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_plan_template_config_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_plan_template_config" RENAME CONSTRAINT "edu_plan_template_config_pkey" TO "edu_plan_template_config_pkey_EFCD72E7";

ALTER TABLE "middle_edu"."edu_rollcall_common_locations" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_rollcall_common_locations_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_rollcall_common_locations" RENAME CONSTRAINT "edu_rollcall_common_locations_pkey" TO "edu_rollcall_common_locations_pkey_F9D84D7B";

ALTER TABLE "middle_edu"."edu_rollcall_record" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_rollcall_record_id_seq2'::regclass);

ALTER TABLE "middle_edu"."edu_rollcall_record" RENAME CONSTRAINT "edu_rollcall_record_pkey" TO "edu_rollcall_record_pkey_3BC0962D";

ALTER TABLE "middle_edu"."edu_rollcall_sign_in" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_rollcall_sign_in_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_rollcall_sign_in" RENAME CONSTRAINT "edu_rollcall_sign_in_pkey" TO "edu_rollcall_sign_in_pkey_B48C6527";

ALTER TABLE "middle_edu"."edu_rule_location" ALTER COLUMN "location_name" TYPE text COLLATE "pg_catalog"."default" USING "location_name"::text;

ALTER TABLE "middle_edu"."edu_rule_location" ALTER COLUMN "longitude" TYPE text COLLATE "pg_catalog"."default" USING "longitude"::text;

ALTER TABLE "middle_edu"."edu_rule_location" ALTER COLUMN "latitude" TYPE text COLLATE "pg_catalog"."default" USING "latitude"::text;

ALTER TABLE "middle_edu"."edu_rule_location" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_rule_location" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_rule_location" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_rule_location_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_rule_location" RENAME CONSTRAINT "con_middle_edu_rule_location_constraint_1" TO "con_middle_edu_rule_location_constraint_1_D9E56194";

ALTER TABLE "middle_edu"."edu_rule_template" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_rule_template_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_rule_template" ALTER COLUMN "rule_name" TYPE text COLLATE "pg_catalog"."default" USING "rule_name"::text;

ALTER TABLE "middle_edu"."edu_rule_template" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_rule_template" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_rule_template" RENAME CONSTRAINT "con_middle_edu_rule_template_constraint_1" TO "con_middle_edu_rule_template_constraint_1_7E2C6374";

ALTER TABLE "middle_edu"."edu_school_accommodation_attendance" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_school_accommodation_attendance_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_school_accommodation_attendance" ALTER COLUMN "clock_date" TYPE timestamp(0) USING "clock_date"::timestamp(0);

ALTER TABLE "middle_edu"."edu_school_accommodation_attendance" RENAME CONSTRAINT "con_middle_edu_school_accommodation_attendance_constraint_1" TO "con_middle_edu_school_accommodation_attendance_constraint_1_895";

ALTER TABLE "middle_edu"."edu_sign_up_unit" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_sign_up_unit_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_sign_up_unit" ALTER COLUMN "unit_name" TYPE text COLLATE "pg_catalog"."default" USING "unit_name"::text;

ALTER TABLE "middle_edu"."edu_sign_up_unit" ALTER COLUMN "unit_charge_people" TYPE text COLLATE "pg_catalog"."default" USING "unit_charge_people"::text;

ALTER TABLE "middle_edu"."edu_sign_up_unit" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."edu_sign_up_unit" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_sign_up_unit" ALTER COLUMN "office_phone" TYPE text COLLATE "pg_catalog"."default" USING "office_phone"::text;

ALTER TABLE "middle_edu"."edu_sign_up_unit" RENAME CONSTRAINT "con_middle_edu_sign_up_unit_constraint_1" TO "con_middle_edu_sign_up_unit_constraint_1_DC2EBE7A";

ALTER TABLE "middle_edu"."edu_system_setting" ALTER COLUMN "reminder" TYPE text COLLATE "pg_catalog"."default" USING "reminder"::text;

ALTER TABLE "middle_edu"."edu_system_setting" ALTER COLUMN "reminder_name" TYPE text COLLATE "pg_catalog"."default" USING "reminder_name"::text;

ALTER TABLE "middle_edu"."edu_system_setting" RENAME CONSTRAINT "edu_system_setting_pkey" TO "edu_system_setting_pkey_E7F05B87";

ALTER TABLE "middle_edu"."edu_teacher_course_information" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_teacher_course_information_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_teacher_course_information" RENAME CONSTRAINT "edu_teacher_course_information_pkey" TO "edu_teacher_course_information_pkey_28540127";

ALTER TABLE "middle_edu"."edu_teacher_dept" ADD CONSTRAINT "edu_teacher_dept_pkey_25EE91C7" PRIMARY KEY ("id");

ALTER TABLE "middle_edu"."edu_teacher_information" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_teacher_information_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_teacher_information" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."edu_teacher_information" ALTER COLUMN "id_number_des" TYPE text COLLATE "pg_catalog"."default" USING "id_number_des"::text;

ALTER TABLE "middle_edu"."edu_teacher_information" ALTER COLUMN "deposit_bank" TYPE text COLLATE "pg_catalog"."default" USING "deposit_bank"::text;

ALTER TABLE "middle_edu"."edu_teacher_information" ALTER COLUMN "bank_card_number" TYPE text COLLATE "pg_catalog"."default" USING "bank_card_number"::text;

ALTER TABLE "middle_edu"."edu_teacher_information" RENAME CONSTRAINT "id" TO "id_1C8AAC0C";

ALTER TABLE "middle_edu"."edu_todo_items" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_todo_items_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_todo_items" RENAME CONSTRAINT "edu_todo_items_pkey" TO "edu_todo_items_pkey_2F65B67";

ALTER TABLE "middle_edu"."edu_train_file" ALTER COLUMN "id" SET DEFAULT nextval('"middle_edu".edu_train_file_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_train_file" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."edu_train_file" ALTER COLUMN "file" TYPE text COLLATE "pg_catalog"."default" USING "file"::text;

ALTER TABLE "middle_edu"."edu_train_file" ALTER COLUMN "user_name" TYPE text COLLATE "pg_catalog"."default" USING "user_name"::text;

ALTER TABLE "middle_edu"."edu_train_file" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_train_file" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "sex" TYPE text COLLATE "pg_catalog"."default" USING "sex"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "card_no" TYPE text COLLATE "pg_catalog"."default" USING "card_no"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "unit_name" TYPE text COLLATE "pg_catalog"."default" USING "unit_name"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "position" TYPE text COLLATE "pg_catalog"."default" USING "position"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "graduation_school" TYPE text COLLATE "pg_catalog"."default" USING "graduation_school"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "remark" TYPE text COLLATE "pg_catalog"."default" USING "remark"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "create_time" TYPE timestamp(0) USING "create_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "update_time" TYPE timestamp(0) USING "update_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "photo" TYPE text COLLATE "pg_catalog"."default" USING "photo"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "report_time" TYPE timestamp(0) USING "report_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "user_id" SET DEFAULT nextval('"middle_edu".edu_trainee_user_id_seq1'::regclass);

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "dropout_reason" TYPE text COLLATE "pg_catalog"."default" USING "dropout_reason"::text;

COMMENT ON COLUMN "middle_edu"."edu_trainee"."height" IS '身高';

COMMENT ON COLUMN "middle_edu"."edu_trainee"."weight" IS '体重';

COMMENT ON COLUMN "middle_edu"."edu_trainee"."clothing_size" IS '尺码';

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "record_address" TYPE text COLLATE "pg_catalog"."default" USING "record_address"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "record_detail_address" TYPE text COLLATE "pg_catalog"."default" USING "record_detail_address"::text;

ALTER TABLE "middle_edu"."edu_trainee" ALTER COLUMN "record_address_name" TYPE text COLLATE "pg_catalog"."default" USING "record_address_name"::text;

COMMENT ON COLUMN "middle_edu"."edu_trainee"."employment_time" IS '任职时间';

COMMENT ON COLUMN "middle_edu"."edu_trainee"."last_training_class" IS '最近培训班次';

ALTER TABLE "middle_edu"."edu_trainee" RENAME CONSTRAINT "con_middle_edu_trainee_constraint_1" TO "con_middle_edu_trainee_constraint_1_D1F6DD54";

ALTER TABLE "middle_edu"."edu_trainee_group" DROP CONSTRAINT "con_middle_edu_trainee_group_constraint_1";

ALTER TABLE "middle_edu"."edu_trainee_group" ALTER COLUMN "group_name" TYPE text COLLATE "pg_catalog"."default" USING "group_name"::text;

ALTER TABLE "middle_edu"."edu_trainee_group" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."edu_trainee_group" ALTER COLUMN "create_time" TYPE timestamp(0) USING "create_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_trainee_group" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."edu_trainee_group" ALTER COLUMN "update_time" TYPE timestamp(0) USING "update_time"::timestamp(0);

ALTER TABLE "middle_edu"."edu_trainee_leave" ALTER COLUMN "status" TYPE text COLLATE "pg_catalog"."default" USING "status"::text;

ALTER TABLE "middle_edu"."edu_trainee_leave" ALTER COLUMN "reason" TYPE text COLLATE "pg_catalog"."default" USING "reason"::text;

ALTER TABLE "middle_edu"."edu_trainee_leave" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."edu_trainee_leave_process" ALTER COLUMN "user_name" TYPE text COLLATE "pg_catalog"."default" USING "user_name"::text;

ALTER TABLE "middle_edu"."edu_trainee_leave_process" ALTER COLUMN "task_name" TYPE text COLLATE "pg_catalog"."default" USING "task_name"::text;

ALTER TABLE "middle_edu"."edu_trainee_leave_process" ALTER COLUMN "task_comment" TYPE text COLLATE "pg_catalog"."default" USING "task_comment"::text;

ALTER TABLE "middle_edu"."edu_trainee_leave_process" ALTER COLUMN "user_type" TYPE text COLLATE "pg_catalog"."default" USING "user_type"::text;

ALTER TABLE "middle_edu"."edu_xcxmsg_config" DROP CONSTRAINT "edu_xcxmsg_config_pkey";

ALTER TABLE "middle_edu"."edu_xcxmsg_config" ALTER COLUMN "inform_time" TYPE text COLLATE "pg_catalog"."default" USING "inform_time"::text;

ALTER TABLE "middle_edu"."edu_xcxmsg_config" ALTER COLUMN "tag" TYPE int2 USING "tag"::int2;

ALTER TABLE "middle_edu"."edu_xcxmsg_his" DROP CONSTRAINT "edu_xcxmsg_his_pkey";

ALTER TABLE "middle_edu"."flw_channel_definition" DROP CONSTRAINT "act_idx_channel_def_uniq_ce734758_45F9851B_EC650C06";

ALTER TABLE "middle_edu"."flw_channel_definition" DROP CONSTRAINT "primary_3da69487_23FF6848_9F865D40";

ALTER TABLE "middle_edu"."flw_ev_databasechangeloglock" DROP CONSTRAINT "primary_20a9ee1f_CBB8BC5B_FD453817";

ALTER TABLE "middle_edu"."flw_event_definition" DROP CONSTRAINT "act_idx_event_def_uniq_bfdbc838_3F395833_34871BE8";

ALTER TABLE "middle_edu"."flw_event_definition" DROP CONSTRAINT "primary_c812ea1e_F622EBEB_A098467A";

ALTER TABLE "middle_edu"."flw_event_deployment" DROP CONSTRAINT "primary_6f92b6ec_CF786348_B19E7C82";

ALTER TABLE "middle_edu"."flw_event_resource" DROP CONSTRAINT "primary_c043403_26358497_D1398138";

ALTER TABLE "middle_edu"."flw_ru_batch" DROP CONSTRAINT "primary_16a222f8_2802982F_605B7245";

ALTER TABLE "middle_edu"."flw_ru_batch_part" DROP CONSTRAINT "flw_fk_batch_part_parent_41c859ba_A5391182_A671179B";

ALTER TABLE "middle_edu"."flw_ru_batch_part" DROP CONSTRAINT "primary_7e441e7e_389B45A1_6BE31A6F";

ALTER TABLE "middle_edu"."hki_course" DROP CONSTRAINT "hki_course_pkey";

ALTER TABLE "middle_edu"."hki_course" ALTER COLUMN "kcid" TYPE text COLLATE "pg_catalog"."default" USING "kcid"::text;

ALTER TABLE "middle_edu"."hki_users" DROP CONSTRAINT "hki_users_pkey";

ALTER TABLE "middle_edu"."home_schedule" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" DROP CONSTRAINT "con_middle_hr_personnal_basic_information_constraint_1";

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "work_id" TYPE text COLLATE "pg_catalog"."default" USING "work_id"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "id_number" TYPE text COLLATE "pg_catalog"."default" USING "id_number"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "photo" TYPE text COLLATE "pg_catalog"."default" USING "photo"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "age" TYPE text COLLATE "pg_catalog"."default" USING "age"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "mobile" TYPE text COLLATE "pg_catalog"."default" USING "mobile"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "native_place" TYPE text COLLATE "pg_catalog"."default" USING "native_place"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "address" TYPE text COLLATE "pg_catalog"."default" USING "address"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "subject_room" TYPE text COLLATE "pg_catalog"."default" USING "subject_room"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "email" TYPE text COLLATE "pg_catalog"."default" USING "email"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."hr_personnel_basic_information" ALTER COLUMN "id_number_des" TYPE text COLLATE "pg_catalog"."default" USING "id_number_des"::text;

ALTER TABLE "middle_edu"."hr_personnel_party_member_file" DROP CONSTRAINT "con_middle_personnal_party_member_file_constraint_1";

ALTER TABLE "middle_edu"."hr_personnel_party_member_file" ALTER COLUMN "party_recommend_person" TYPE text COLLATE "pg_catalog"."default" USING "party_recommend_person"::text;

ALTER TABLE "middle_edu"."hr_personnel_party_member_file" ALTER COLUMN "award_situation" TYPE text COLLATE "pg_catalog"."default" USING "award_situation"::text;

ALTER TABLE "middle_edu"."hr_personnel_party_member_file" ALTER COLUMN "punish_situation" TYPE text COLLATE "pg_catalog"."default" USING "punish_situation"::text;

ALTER TABLE "middle_edu"."hr_personnel_party_member_file" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."hr_personnel_party_member_file" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."hr_personnel_position" DROP CONSTRAINT "con_middle_personnal_position_constraint_1";

ALTER TABLE "middle_edu"."hr_personnel_position" ALTER COLUMN "administrative_position_name" TYPE text COLLATE "pg_catalog"."default" USING "administrative_position_name"::text;

ALTER TABLE "middle_edu"."hr_personnel_position" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."hr_personnel_position" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" DROP CONSTRAINT "con_middle_personnel_basic_information_constraint_1";

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "work_id" TYPE text COLLATE "pg_catalog"."default" USING "work_id"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "id_number" TYPE text COLLATE "pg_catalog"."default" USING "id_number"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "mobile" TYPE text COLLATE "pg_catalog"."default" USING "mobile"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "email" TYPE text COLLATE "pg_catalog"."default" USING "email"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."hr_personnel_registration" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."hr_personnel_study_experience" DROP CONSTRAINT "con_middle_personnal_study_experience_constraint_1";

ALTER TABLE "middle_edu"."hr_personnel_study_experience" ALTER COLUMN "school" TYPE text COLLATE "pg_catalog"."default" USING "school"::text;

ALTER TABLE "middle_edu"."hr_personnel_study_experience" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."hr_personnel_study_experience" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" DROP CONSTRAINT "cons134219671_985d9614_F1150FA9_B090AEC3_7B494865_1_1_2";

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "out_subject_room" TYPE text COLLATE "pg_catalog"."default" USING "out_subject_room"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "out_administrative_position_name" TYPE text COLLATE "pg_catalog"."default" USING "out_administrative_position_name"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "in_subject_room" TYPE text COLLATE "pg_catalog"."default" USING "in_subject_room"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "in_administrative_position_name" TYPE text COLLATE "pg_catalog"."default" USING "in_administrative_position_name"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "transfer_reason" TYPE text COLLATE "pg_catalog"."default" USING "transfer_reason"::text;

ALTER TABLE "middle_edu"."hr_personnel_transfer" ALTER COLUMN "notes" TYPE text COLLATE "pg_catalog"."default" USING "notes"::text;

ALTER TABLE "middle_edu"."hr_personnel_work_experience" DROP CONSTRAINT "con_middle_personnal_work_experience_constraint_1";

ALTER TABLE "middle_edu"."hr_personnel_work_experience" ALTER COLUMN "work_unit" TYPE text COLLATE "pg_catalog"."default" USING "work_unit"::text;

ALTER TABLE "middle_edu"."hr_personnel_work_experience" ALTER COLUMN "position" TYPE text COLLATE "pg_catalog"."default" USING "position"::text;

ALTER TABLE "middle_edu"."hr_personnel_work_experience" ALTER COLUMN "work_content" TYPE text COLLATE "pg_catalog"."default" USING "work_content"::text;

ALTER TABLE "middle_edu"."hr_personnel_work_experience" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."hr_personnel_work_experience" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."hr_recruit_accessory_info" ALTER COLUMN "accessory_name" TYPE text COLLATE "pg_catalog"."default" USING "accessory_name"::text;

ALTER TABLE "middle_edu"."hr_recruit_accessory_info" ALTER COLUMN "accessory_type" TYPE text COLLATE "pg_catalog"."default" USING "accessory_type"::text;

ALTER TABLE "middle_edu"."hr_recruit_accessory_info" ALTER COLUMN "accessory" TYPE text COLLATE "pg_catalog"."default" USING "accessory"::text;

ALTER TABLE "middle_edu"."hr_recruit_accessory_info" ALTER COLUMN "accessory_sort" TYPE text COLLATE "pg_catalog"."default" USING "accessory_sort"::text;

ALTER TABLE "middle_edu"."hr_recruit_accessory_info" ALTER COLUMN "file" TYPE text COLLATE "pg_catalog"."default" USING "file"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "id_number" TYPE text COLLATE "pg_catalog"."default" USING "id_number"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "nation" TYPE text COLLATE "pg_catalog"."default" USING "nation"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "politics_status" TYPE text COLLATE "pg_catalog"."default" USING "politics_status"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "education" TYPE text COLLATE "pg_catalog"."default" USING "education"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "degree" TYPE text COLLATE "pg_catalog"."default" USING "degree"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "school" TYPE text COLLATE "pg_catalog"."default" USING "school"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "major" TYPE text COLLATE "pg_catalog"."default" USING "major"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "professional_title" TYPE text COLLATE "pg_catalog"."default" USING "professional_title"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "domicile" TYPE text COLLATE "pg_catalog"."default" USING "domicile"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "native_place" TYPE text COLLATE "pg_catalog"."default" USING "native_place"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "fresh_graduate" TYPE text COLLATE "pg_catalog"."default" USING "fresh_graduate"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "archival_custodian_unit" TYPE text COLLATE "pg_catalog"."default" USING "archival_custodian_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "strong_point" TYPE text COLLATE "pg_catalog"."default" USING "strong_point"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "current_unit_position" TYPE text COLLATE "pg_catalog"."default" USING "current_unit_position"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "marry_status" TYPE text COLLATE "pg_catalog"."default" USING "marry_status"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "resume_info" TYPE text COLLATE "pg_catalog"."default" USING "resume_info"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "email" TYPE text COLLATE "pg_catalog"."default" USING "email"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "address" TYPE text COLLATE "pg_catalog"."default" USING "address"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "postal_code" TYPE text COLLATE "pg_catalog"."default" USING "postal_code"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "job_title" TYPE text COLLATE "pg_catalog"."default" USING "job_title"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "application_number" TYPE text COLLATE "pg_catalog"."default" USING "application_number"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "recruitment_unit" TYPE text COLLATE "pg_catalog"."default" USING "recruitment_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "image" TYPE text COLLATE "pg_catalog"."default" USING "image"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "grade" TYPE text COLLATE "pg_catalog"."default" USING "grade"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "first_approve" TYPE text COLLATE "pg_catalog"."default" USING "first_approve"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "face_approve" TYPE text COLLATE "pg_catalog"."default" USING "face_approve"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "household_type" TYPE text COLLATE "pg_catalog"."default" USING "household_type"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "residence" TYPE text COLLATE "pg_catalog"."default" USING "residence"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "qualification_certificate" TYPE text COLLATE "pg_catalog"."default" USING "qualification_certificate"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "reward_and_punishment" TYPE text COLLATE "pg_catalog"."default" USING "reward_and_punishment"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "modify_suggestion" TYPE text COLLATE "pg_catalog"."default" USING "modify_suggestion"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "post_code" TYPE text COLLATE "pg_catalog"."default" USING "post_code"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "professional_qualification" TYPE text COLLATE "pg_catalog"."default" USING "professional_qualification"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info" ALTER COLUMN "recruitment_dept" TYPE text COLLATE "pg_catalog"."default" USING "recruitment_dept"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "card_no" TYPE text COLLATE "pg_catalog"."default" USING "card_no"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "nation" TYPE text COLLATE "pg_catalog"."default" USING "nation"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "politics_status" TYPE text COLLATE "pg_catalog"."default" USING "politics_status"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "highest_education_level" TYPE text COLLATE "pg_catalog"."default" USING "highest_education_level"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "highest_academic_degree" TYPE text COLLATE "pg_catalog"."default" USING "highest_academic_degree"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "highest_graduate_school" TYPE text COLLATE "pg_catalog"."default" USING "highest_graduate_school"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "major" TYPE text COLLATE "pg_catalog"."default" USING "major"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "professional_title" TYPE text COLLATE "pg_catalog"."default" USING "professional_title"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "domicile" TYPE text COLLATE "pg_catalog"."default" USING "domicile"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "native_place" TYPE text COLLATE "pg_catalog"."default" USING "native_place"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "fresh_graduate" TYPE text COLLATE "pg_catalog"."default" USING "fresh_graduate"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "archival_custodian_unit" TYPE text COLLATE "pg_catalog"."default" USING "archival_custodian_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "strong_point" TYPE text COLLATE "pg_catalog"."default" USING "strong_point"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "current_unit_position" TYPE text COLLATE "pg_catalog"."default" USING "current_unit_position"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "marital_status" TYPE text COLLATE "pg_catalog"."default" USING "marital_status"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "resume_info" TYPE text COLLATE "pg_catalog"."default" USING "resume_info"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "mailbox" TYPE text COLLATE "pg_catalog"."default" USING "mailbox"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "address" TYPE text COLLATE "pg_catalog"."default" USING "address"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "postal_code" TYPE text COLLATE "pg_catalog"."default" USING "postal_code"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "job_title" TYPE text COLLATE "pg_catalog"."default" USING "job_title"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "application_number" TYPE text COLLATE "pg_catalog"."default" USING "application_number"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "recruitment_unit" TYPE text COLLATE "pg_catalog"."default" USING "recruitment_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "head_url" TYPE text COLLATE "pg_catalog"."default" USING "head_url"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "grade" TYPE text COLLATE "pg_catalog"."default" USING "grade"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "first_approve" TYPE text COLLATE "pg_catalog"."default" USING "first_approve"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "face_approve" TYPE text COLLATE "pg_catalog"."default" USING "face_approve"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "household_type" TYPE text COLLATE "pg_catalog"."default" USING "household_type"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "residence" TYPE text COLLATE "pg_catalog"."default" USING "residence"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "qualification_certificate" TYPE text COLLATE "pg_catalog"."default" USING "qualification_certificate"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "reward_and_punishment" TYPE text COLLATE "pg_catalog"."default" USING "reward_and_punishment"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "modify_suggestion" TYPE text COLLATE "pg_catalog"."default" USING "modify_suggestion"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "post_code" TYPE text COLLATE "pg_catalog"."default" USING "post_code"::text;

ALTER TABLE "middle_edu"."hr_recruit_basic_info_back" ALTER COLUMN "professional_qualification" TYPE text COLLATE "pg_catalog"."default" USING "professional_qualification"::text;

ALTER TABLE "middle_edu"."hr_recruit_learning_exp" ALTER COLUMN "education_level" TYPE text COLLATE "pg_catalog"."default" USING "education_level"::text;

ALTER TABLE "middle_edu"."hr_recruit_learning_exp" ALTER COLUMN "academic_degree" TYPE text COLLATE "pg_catalog"."default" USING "academic_degree"::text;

ALTER TABLE "middle_edu"."hr_recruit_learning_exp" ALTER COLUMN "school" TYPE text COLLATE "pg_catalog"."default" USING "school"::text;

ALTER TABLE "middle_edu"."hr_recruit_learning_exp" ALTER COLUMN "research_direction" TYPE text COLLATE "pg_catalog"."default" USING "research_direction"::text;

ALTER TABLE "middle_edu"."hr_recruit_learning_exp" ALTER COLUMN "is_full_time" TYPE text COLLATE "pg_catalog"."default" USING "is_full_time"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_book" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_book" ALTER COLUMN "publication_unit" TYPE text COLLATE "pg_catalog"."default" USING "publication_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_book" ALTER COLUMN "ranking" TYPE text COLLATE "pg_catalog"."default" USING "ranking"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_paper" ALTER COLUMN "thesis_title" TYPE text COLLATE "pg_catalog"."default" USING "thesis_title"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_paper" ALTER COLUMN "publication_title" TYPE text COLLATE "pg_catalog"."default" USING "publication_title"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_paper" ALTER COLUMN "thesis_level" TYPE text COLLATE "pg_catalog"."default" USING "thesis_level"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_paper" ALTER COLUMN "ranking" TYPE text COLLATE "pg_catalog"."default" USING "ranking"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_result" ALTER COLUMN "outcome_name" TYPE text COLLATE "pg_catalog"."default" USING "outcome_name"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_result" ALTER COLUMN "instruction_situation" TYPE text COLLATE "pg_catalog"."default" USING "instruction_situation"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_result" ALTER COLUMN "journal_level" TYPE text COLLATE "pg_catalog"."default" USING "journal_level"::text;

ALTER TABLE "middle_edu"."hr_recruit_public_result" ALTER COLUMN "ranking" TYPE text COLLATE "pg_catalog"."default" USING "ranking"::text;

ALTER TABLE "middle_edu"."hr_recruit_relationship" ALTER COLUMN "relation" TYPE text COLLATE "pg_catalog"."default" USING "relation"::text;

ALTER TABLE "middle_edu"."hr_recruit_relationship" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."hr_recruit_relationship" ALTER COLUMN "political_status" TYPE text COLLATE "pg_catalog"."default" USING "political_status"::text;

ALTER TABLE "middle_edu"."hr_recruit_relationship" ALTER COLUMN "work_unit_and_position" TYPE text COLLATE "pg_catalog"."default" USING "work_unit_and_position"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_achievement" ALTER COLUMN "award_name" TYPE text COLLATE "pg_catalog"."default" USING "award_name"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_achievement" ALTER COLUMN "award_unit" TYPE text COLLATE "pg_catalog"."default" USING "award_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_achievement" ALTER COLUMN "award_lever" TYPE text COLLATE "pg_catalog"."default" USING "award_lever"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_achievement" ALTER COLUMN "ranking" TYPE text COLLATE "pg_catalog"."default" USING "ranking"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_project" ALTER COLUMN "project_name" TYPE text COLLATE "pg_catalog"."default" USING "project_name"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_project" ALTER COLUMN "project_level" TYPE text COLLATE "pg_catalog"."default" USING "project_level"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_project" ALTER COLUMN "personal_role" TYPE text COLLATE "pg_catalog"."default" USING "personal_role"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_project" ALTER COLUMN "ranking" TYPE text COLLATE "pg_catalog"."default" USING "ranking"::text;

ALTER TABLE "middle_edu"."hr_recruit_research_project" ALTER COLUMN "approving_department" TYPE text COLLATE "pg_catalog"."default" USING "approving_department"::text;

ALTER TABLE "middle_edu"."hr_recruit_teaching_award" ALTER COLUMN "award_name" TYPE text COLLATE "pg_catalog"."default" USING "award_name"::text;

ALTER TABLE "middle_edu"."hr_recruit_teaching_award" ALTER COLUMN "award_unit" TYPE text COLLATE "pg_catalog"."default" USING "award_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_teaching_award" ALTER COLUMN "award_lever" TYPE text COLLATE "pg_catalog"."default" USING "award_lever"::text;

ALTER TABLE "middle_edu"."hr_recruit_teaching_award" ALTER COLUMN "ranking" TYPE text COLLATE "pg_catalog"."default" USING "ranking"::text;

ALTER TABLE "middle_edu"."hr_recruit_work_exp" ALTER COLUMN "work_unit" TYPE text COLLATE "pg_catalog"."default" USING "work_unit"::text;

ALTER TABLE "middle_edu"."hr_recruit_work_exp" ALTER COLUMN "position" TYPE text COLLATE "pg_catalog"."default" USING "position"::text;

ALTER TABLE "middle_edu"."hr_recruitment_batch_management" ALTER COLUMN "batch_name" TYPE text COLLATE "pg_catalog"."default" USING "batch_name"::text;

ALTER TABLE "middle_edu"."hr_recruitment_batch_management" ALTER COLUMN "batch_class" TYPE text COLLATE "pg_catalog"."default" USING "batch_class"::text;

ALTER TABLE "middle_edu"."hr_recruitment_batch_management" ALTER COLUMN "batch_status" TYPE text COLLATE "pg_catalog"."default" USING "batch_status"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" DROP CONSTRAINT "hr_recruitment_position_management_pk";

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "recruitment_unit" TYPE text COLLATE "pg_catalog"."default" USING "recruitment_unit"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "position_name" TYPE text COLLATE "pg_catalog"."default" USING "position_name"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "post_code" TYPE text COLLATE "pg_catalog"."default" USING "post_code"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "recruitment_plan" TYPE text COLLATE "pg_catalog"."default" USING "recruitment_plan"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "academic_degree" TYPE text COLLATE "pg_catalog"."default" USING "academic_degree"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "age" TYPE text COLLATE "pg_catalog"."default" USING "age"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "profession" TYPE text COLLATE "pg_catalog"."default" USING "profession"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "other" TYPE text COLLATE "pg_catalog"."default" USING "other"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "remark" TYPE text COLLATE "pg_catalog"."default" USING "remark"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "job_requirements" TYPE text COLLATE "pg_catalog"."default" USING "job_requirements"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "job_responsibility" TYPE text COLLATE "pg_catalog"."default" USING "job_responsibility"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "release_status" TYPE text COLLATE "pg_catalog"."default" USING "release_status"::text;

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "updater" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "updater"::varchar(255);

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "creator" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "creator"::varchar(255);

ALTER TABLE "middle_edu"."hr_recruitment_position_management" ALTER COLUMN "recruitment_dept" TYPE text COLLATE "pg_catalog"."default" USING "recruitment_dept"::text;

ALTER TABLE "middle_edu"."infra_api_access_log" DROP CONSTRAINT "cons134219974_83ca77b3_36507AE6_4997B922_37F492C7";

ALTER TABLE "middle_edu"."infra_api_error_log" DROP CONSTRAINT "infra_api_error_log_copy1_pkey";

ALTER TABLE "middle_edu"."infra_api_error_logback01" DROP CONSTRAINT "cons134219319_86de76d4_F148A129_6466EE4C_5F276470";

ALTER TABLE "middle_edu"."infra_codegen_column" DROP CONSTRAINT "cons134219356_2928fb5_A4A33142_E5843E4E_C0BF2490";

ALTER TABLE "middle_edu"."infra_codegen_table" DROP CONSTRAINT "cons134219357_87ad3018_B8AB703D_6CF8D1D3_F13DEEF7";

ALTER TABLE "middle_edu"."infra_data_source_config" DROP CONSTRAINT "cons134219359_4282bb17_81295956_AA6C45BE_DE6E7602";

ALTER TABLE "middle_edu"."infra_file" DROP CONSTRAINT "cons134219360_8e423298_65FE8E40_B6FE39D6_D02A17C4";

ALTER TABLE "middle_edu"."infra_file_config" DROP CONSTRAINT "cons134219361_49a63804_D8767CCE_589EAB22_E3D752DD";

ALTER TABLE "middle_edu"."infra_file_config" ALTER COLUMN "name" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "name"::varchar(255);

ALTER TABLE "middle_edu"."infra_file_config" ALTER COLUMN "remark" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "remark"::varchar(255);

ALTER TABLE "middle_edu"."infra_file_config" ALTER COLUMN "config" TYPE varchar(2550) COLLATE "pg_catalog"."default" USING "config"::varchar(2550);

ALTER TABLE "middle_edu"."infra_file_config" ALTER COLUMN "creator" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "creator"::varchar(255);

ALTER TABLE "middle_edu"."infra_file_config" ALTER COLUMN "updater" TYPE varchar(255) COLLATE "pg_catalog"."default" USING "updater"::varchar(255);

ALTER TABLE "middle_edu"."infra_file_content" DROP CONSTRAINT "cons134219362_35522d34_D18B14AD_AA58033F_C2511BA9";

ALTER TABLE "middle_edu"."infra_job" DROP CONSTRAINT "cons134219363_30a2d93e_22A83906_4BCF8C8C_7D93804A";

ALTER TABLE "middle_edu"."infra_job_log" DROP CONSTRAINT "cons134219364_ac7cdde4_AF0041E8_72CF4C5C_17E2972E";

COMMENT ON COLUMN "middle_edu"."loudi_edu_cadre_information"."last_training_class" IS '最近培训班次';

ALTER TABLE "middle_edu"."loudi_pg_options" DROP CONSTRAINT "con_middle_edu_loudi_pg_options_constraint_1";

ALTER TABLE "middle_edu"."loudi_pg_question_category_management" DROP CONSTRAINT "con_middle_edu_loudi_pg_question_category_management_constraint";

ALTER TABLE "middle_edu"."loudi_pg_question_logic" DROP CONSTRAINT "con_middle_edu_loudi_pg_question_logic_constraint_1";

ALTER TABLE "middle_edu"."loudi_pg_questionnaire_detail" DROP CONSTRAINT "con_middle_edu_loudi_pg_questionnaire_detail_constraint_1";

ALTER TABLE "middle_edu"."loudi_pg_questionnaire_his" DROP CONSTRAINT "con_middle_edu_loudi_pg_questionnaire_his_constraint_1";

ALTER TABLE "middle_edu"."loudi_pg_questionnaire_management" DROP CONSTRAINT "pk_id";

ALTER TABLE "middle_edu"."merchant_details" DROP CONSTRAINT "merchant_details_pkey";

ALTER TABLE "middle_edu"."midoffice_schedule" DROP CONSTRAINT "cons134219533_1fea7567_3BF7F1F8_FE187614_8302CDE3";

ALTER TABLE "middle_edu"."midoffice_shortcut" DROP CONSTRAINT "cons134219535_9cfd9fd8_5A42C840_7745BB8A_AF27FDEF";

ALTER TABLE "middle_edu"."midoffice_shortcut_user" DROP CONSTRAINT "cons134219632_83067e5e_F72FB070_84C6A56B_710DEAB3";

ALTER TABLE "middle_edu"."midoffice_todo" DROP CONSTRAINT "cons134219699_24F4DAB7_844EE11A";

ALTER TABLE "middle_edu"."oa_central_task" DROP CONSTRAINT "sys_yeartask_pkey";

ALTER TABLE "middle_edu"."oa_central_task_extend" DROP CONSTRAINT "yeartask_userinfor_pkey";

ALTER TABLE "middle_edu"."oa_central_task_operation" DROP CONSTRAINT "yeartask_operation_pkey";

ALTER TABLE "middle_edu"."oa_central_task_operation" ALTER COLUMN "nickname" TYPE text COLLATE "pg_catalog"."default" USING "nickname"::text;

ALTER TABLE "middle_edu"."oa_central_task_operation" ALTER COLUMN "operation_user_id" TYPE text COLLATE "pg_catalog"."default" USING "operation_user_id"::text;

ALTER TABLE "middle_edu"."oa_central_task_operation" ALTER COLUMN "operation_dept_name" TYPE text COLLATE "pg_catalog"."default" USING "operation_dept_name"::text;

ALTER TABLE "middle_edu"."oa_central_task_operation" ALTER COLUMN "operation_dept_id" TYPE text COLLATE "pg_catalog"."default" USING "operation_dept_id"::text;

ALTER TABLE "middle_edu"."oa_central_task_operation" ALTER COLUMN "rejection_reason" TYPE text COLLATE "pg_catalog"."default" USING "rejection_reason"::text;

ALTER TABLE "middle_edu"."oa_central_task_record" ALTER COLUMN "files" TYPE text COLLATE "pg_catalog"."default" USING "files"::text;

ALTER TABLE "middle_edu"."oa_central_task_record" ALTER COLUMN "user_name" TYPE text COLLATE "pg_catalog"."default" USING "user_name"::text;

ALTER TABLE "middle_edu"."oa_central_task_record" ALTER COLUMN "user_id" TYPE text COLLATE "pg_catalog"."default" USING "user_id"::text;

ALTER TABLE "middle_edu"."oa_draft" ALTER COLUMN "process_category" TYPE text COLLATE "pg_catalog"."default" USING "process_category"::text;

ALTER TABLE "middle_edu"."oa_draft" ALTER COLUMN "launch_user_id" TYPE text COLLATE "pg_catalog"."default" USING "launch_user_id"::text;

ALTER TABLE "middle_edu"."oa_draft" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_draft" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_leave" DROP CONSTRAINT "cons134219312_7f1fcb7e_BFDD3CA2_1E0C70F2_7585B069";

ALTER TABLE "middle_edu"."oa_leave" ALTER COLUMN "files" TYPE text COLLATE "pg_catalog"."default" USING "files"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "subject_name" TYPE text COLLATE "pg_catalog"."default" USING "subject_name"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "invite_unit" TYPE text COLLATE "pg_catalog"."default" USING "invite_unit"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "lecture_address" TYPE text COLLATE "pg_catalog"."default" USING "lecture_address"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "audience" TYPE text COLLATE "pg_catalog"."default" USING "audience"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "class_period" TYPE text COLLATE "pg_catalog"."default" USING "class_period"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "lecture_content" TYPE text COLLATE "pg_catalog"."default" USING "lecture_content"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "files" TYPE text COLLATE "pg_catalog"."default" USING "files"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "sign_image" TYPE text COLLATE "pg_catalog"."default" USING "sign_image"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_lecture" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_notice" DROP CONSTRAINT "oa_notice_pk";

ALTER TABLE "middle_edu"."oa_out_report" DROP CONSTRAINT "oa_out_report_pk";

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "out_address" TYPE text COLLATE "pg_catalog"."default" USING "out_address"::text;

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "out_content" TYPE text COLLATE "pg_catalog"."default" USING "out_content"::text;

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "files" TYPE text COLLATE "pg_catalog"."default" USING "files"::text;

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "sign" TYPE text COLLATE "pg_catalog"."default" USING "sign"::text;

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_out_report" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "process_category" TYPE text COLLATE "pg_catalog"."default" USING "process_category"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "copy_user_id" TYPE text COLLATE "pg_catalog"."default" USING "copy_user_id"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "launch_user_id" TYPE text COLLATE "pg_catalog"."default" USING "launch_user_id"::text;

ALTER TABLE "middle_edu"."oa_receive" ALTER COLUMN "content" TYPE text COLLATE "pg_catalog"."default" USING "content"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty" DROP CONSTRAINT "oa_vacation_duty_pk";

ALTER TABLE "middle_edu"."oa_vacation_duty" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty" ALTER COLUMN "copy_to" TYPE text COLLATE "pg_catalog"."default" USING "copy_to"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" DROP CONSTRAINT "oa_vacation_duty_form_pk";

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "start_date" TYPE text COLLATE "pg_catalog"."default" USING "start_date"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "end_date" TYPE text COLLATE "pg_catalog"."default" USING "end_date"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "personnel_id" TYPE text COLLATE "pg_catalog"."default" USING "personnel_id"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "dept_id" TYPE text COLLATE "pg_catalog"."default" USING "dept_id"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "duty_type" TYPE text COLLATE "pg_catalog"."default" USING "duty_type"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "telephone" TYPE text COLLATE "pg_catalog"."default" USING "telephone"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form" ALTER COLUMN "personnel" TYPE text COLLATE "pg_catalog"."default" USING "personnel"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "personnel_id" TYPE text COLLATE "pg_catalog"."default" USING "personnel_id"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "dept_ids" TYPE text COLLATE "pg_catalog"."default" USING "dept_ids"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "duty_type" TYPE text COLLATE "pg_catalog"."default" USING "duty_type"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "telephone" TYPE text COLLATE "pg_catalog"."default" USING "telephone"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_1" ALTER COLUMN "personnel" TYPE text COLLATE "pg_catalog"."default" USING "personnel"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" DROP CONSTRAINT "oa_vacation_duty_form_leader_pk";

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "start_date" TYPE text COLLATE "pg_catalog"."default" USING "start_date"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "end_date" TYPE text COLLATE "pg_catalog"."default" USING "end_date"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "vacation_duty_ids" TYPE text COLLATE "pg_catalog"."default" USING "vacation_duty_ids"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "personnel_id" TYPE text COLLATE "pg_catalog"."default" USING "personnel_id"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "dept_id" TYPE text COLLATE "pg_catalog"."default" USING "dept_id"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "duty_type" TYPE text COLLATE "pg_catalog"."default" USING "duty_type"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "telephone" TYPE text COLLATE "pg_catalog"."default" USING "telephone"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "personnel" TYPE text COLLATE "pg_catalog"."default" USING "personnel"::text;

ALTER TABLE "middle_edu"."oa_vacation_duty_form_leader" ALTER COLUMN "dept_name" TYPE text COLLATE "pg_catalog"."default" USING "dept_name"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_schedule" DROP CONSTRAINT "oa_weekly_work_schedule_pk";

ALTER TABLE "middle_edu"."oa_weekly_work_schedule" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_schedule" ALTER COLUMN "copy_to" TYPE text COLLATE "pg_catalog"."default" USING "copy_to"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_schedule" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_schedule" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_summary" ALTER COLUMN "process_instance_id" TYPE text COLLATE "pg_catalog"."default" USING "process_instance_id"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_summary" ALTER COLUMN "weekly_work_schedule_ids" TYPE text COLLATE "pg_catalog"."default" USING "weekly_work_schedule_ids"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_summary" ALTER COLUMN "semester" TYPE text COLLATE "pg_catalog"."default" USING "semester"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_summary" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_summary" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."oa_weekly_work_summary" ALTER COLUMN "copy_to" TYPE text COLLATE "pg_catalog"."default" USING "copy_to"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "location" TYPE text COLLATE "pg_catalog"."default" USING "location"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "activity" TYPE text COLLATE "pg_catalog"."default" USING "activity"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "fill_item" TYPE text COLLATE "pg_catalog"."default" USING "fill_item"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "enrolled_personnel" TYPE text COLLATE "pg_catalog"."default" USING "enrolled_personnel"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "personnel_ids" TYPE text COLLATE "pg_catalog"."default" USING "personnel_ids"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "dept_ids" TYPE text COLLATE "pg_catalog"."default" USING "dept_ids"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."oa_work_schedule" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."old_personnalinfor_base" DROP CONSTRAINT "old_personnalinfor_base_pkey";

ALTER TABLE "middle_edu"."onecard_user" DROP CONSTRAINT "onecard_user_pkey";

ALTER TABLE "middle_edu"."pay_message" ALTER COLUMN "one_card_message" TYPE text COLLATE "pg_catalog"."default" USING "one_card_message"::text;

ALTER TABLE "middle_edu"."pay_message" ALTER COLUMN "out_trade_no" TYPE text COLLATE "pg_catalog"."default" USING "out_trade_no"::text;

ALTER TABLE "middle_edu"."pg_evaluation_detail" ALTER COLUMN "content" TYPE varchar(255) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "middle_edu"."pg_evaluation_detail"."course_id" IS '';

ALTER TABLE "middle_edu"."pg_evaluation_detail" RENAME CONSTRAINT "pg_evaluation_detail_pkey" TO "pg_evaluation_detail_pk";

ALTER TABLE "middle_edu"."pg_evaluation_response" ALTER COLUMN "questionnaire_id" SET DEFAULT 1;

COMMENT ON COLUMN "middle_edu"."pg_evaluation_response"."questionnaire_id" IS '';

ALTER TABLE "middle_edu"."pg_evaluation_response" ALTER COLUMN "class_course_id" SET DEFAULT 1;

COMMENT ON COLUMN "middle_edu"."pg_evaluation_response"."class_course_id" IS '';

COMMENT ON COLUMN "middle_edu"."pg_evaluation_response"."course_id" IS '';

COMMENT ON COLUMN "middle_edu"."pg_evaluation_response"."low_score_reason" IS '最低分理由说明';

COMMENT ON COLUMN "middle_edu"."pg_evaluation_response"."submit_time" IS '问卷提交时间';

COMMENT ON COLUMN "middle_edu"."pg_evaluation_response"."revocable_time" IS '问卷撤回截止时间';

ALTER TABLE "middle_edu"."pg_evaluation_response" RENAME CONSTRAINT "pg_evaluation_response_pkey" TO "pg_evaluation_response_pk";

ALTER TABLE "middle_edu"."pg_options" DROP CONSTRAINT "pg_options_pkey";

ALTER TABLE "middle_edu"."pg_question_category_management" DROP CONSTRAINT "pg_question_category_management_pkey";

ALTER TABLE "middle_edu"."pg_question_logic" DROP CONSTRAINT "pg_question_logic_pkey";

ALTER TABLE "middle_edu"."pg_question_management" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."pg_question_management" RENAME CONSTRAINT "pg_question_management_pkey" TO "pg_question_management_pk";

ALTER TABLE "middle_edu"."pg_questionnaire_detail" DROP CONSTRAINT "pg_questionnaire_detail_pkey";

ALTER TABLE "middle_edu"."pg_questionnaire_management" DROP CONSTRAINT "pg_questionnaire_management_pkey";

ALTER TABLE "middle_edu"."pg_questionnaire_management" ALTER COLUMN "subtitle" TYPE text COLLATE "pg_catalog"."default" USING "subtitle"::text;

COMMENT ON COLUMN "middle_edu"."pg_questionnaire_management"."revocable_tag" IS '启用问卷有效期撤回时限';

COMMENT ON COLUMN "middle_edu"."pg_questionnaire_management"."revocable_time_limit" IS '撤回时限天数（支持小数）';

ALTER TABLE "middle_edu"."system_dept" DROP CONSTRAINT "cons134219759_4b1ecbb0_3D3EB88E_1673B227_88F5B17E";

ALTER TABLE "middle_edu"."system_dict_data" ALTER COLUMN "create_time" SET NOT NULL;

ALTER TABLE "middle_edu"."system_dict_data" ALTER COLUMN "create_time" SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE "middle_edu"."system_dict_data" ALTER COLUMN "update_time" SET NOT NULL;

ALTER TABLE "middle_edu"."system_dict_data" ALTER COLUMN "update_time" SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE "middle_edu"."system_dict_data" ALTER COLUMN "deleted" SET NOT NULL;

COMMENT ON COLUMN "middle_edu"."system_dict_data"."tenant_id" IS '部门id';

ALTER TABLE "middle_edu"."system_dict_data" RENAME CONSTRAINT "system_dict_data_copy1_pkey" TO "cons134219500_f7fb538_C8FF8F26_3582CFD0_A6907E2A_4FAE95CE";

ALTER TABLE "middle_edu"."system_dict_type" DROP CONSTRAINT "system_dict_type_copy1_type_key";

ALTER TABLE "middle_edu"."system_dict_type" ALTER COLUMN "create_time" SET NOT NULL;

ALTER TABLE "middle_edu"."system_dict_type" ALTER COLUMN "update_time" SET NOT NULL;

ALTER TABLE "middle_edu"."system_dict_type" ALTER COLUMN "deleted" SET NOT NULL;

ALTER TABLE "middle_edu"."system_dict_type" RENAME CONSTRAINT "system_dict_type_copy1_pkey" TO "cons134219662_41828045_5321D39A_33EB2869_7BC287CC_B90FBFDC";

ALTER TABLE "middle_edu"."system_dict_type" ADD CONSTRAINT "dict_type_48069889_3636F890_BC3B4D1A_7151E531_F90C14AE" UNIQUE ("type");

ALTER TABLE "middle_edu"."system_error_code" DROP CONSTRAINT "cons134219440_7e427da4_B398ED7_B0793800_28B5A9C4";

ALTER TABLE "middle_edu"."system_login_log" DROP CONSTRAINT "cons134219441_ba5a824f_288AD457_7E3DFF9_2BC5EFF4";

ALTER TABLE "middle_edu"."system_message_authority" DROP CONSTRAINT "cons134219671_985d9614_F1150FA9_B090AEC3_7B494865_1_1";

ALTER TABLE "middle_edu"."system_message_authority" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."system_message_authority" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."system_message_send" DROP CONSTRAINT "con_middle_system_message_send_constraint_1";

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "receiving_mobile" TYPE text COLLATE "pg_catalog"."default" USING "receiving_mobile"::text;

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "message_content" TYPE text COLLATE "pg_catalog"."default" USING "message_content"::text;

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "template_content" TYPE text COLLATE "pg_catalog"."default" USING "template_content"::text;

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "template_name" TYPE text COLLATE "pg_catalog"."default" USING "template_name"::text;

ALTER TABLE "middle_edu"."system_message_send" ALTER COLUMN "receiving_person_names" TYPE text COLLATE "pg_catalog"."default" USING "receiving_person_names"::text;

ALTER TABLE "middle_edu"."system_message_template" DROP CONSTRAINT "con_middle_system_message_constraint_1";

ALTER TABLE "middle_edu"."system_message_template" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."system_message_template" ALTER COLUMN "content" TYPE text COLLATE "pg_catalog"."default" USING "content"::text;

ALTER TABLE "middle_edu"."system_message_template" ALTER COLUMN "remark" TYPE text COLLATE "pg_catalog"."default" USING "remark"::text;

ALTER TABLE "middle_edu"."system_message_template" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."system_message_template" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."system_message_upstream" DROP CONSTRAINT "cons134219671_985d9614_F1150FA9_B090AEC3_7B494865_1_1_1";

ALTER TABLE "middle_edu"."system_message_upstream" ALTER COLUMN "name" TYPE text COLLATE "pg_catalog"."default" USING "name"::text;

ALTER TABLE "middle_edu"."system_message_upstream" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."system_message_upstream" ALTER COLUMN "message_content" TYPE text COLLATE "pg_catalog"."default" USING "message_content"::text;

ALTER TABLE "middle_edu"."system_message_upstream" ALTER COLUMN "cp_moblie" TYPE text COLLATE "pg_catalog"."default" USING "cp_moblie"::text;

ALTER TABLE "middle_edu"."system_notice" DROP CONSTRAINT "cons134219443_7bb0789b_AF1D8897_3E1228E_F197841B";

ALTER TABLE "middle_edu"."system_oauth2_access_token" DROP CONSTRAINT "cons134219444_1dbe7b86_576F8E28_4A2697EC_A7710DF0";

ALTER TABLE "middle_edu"."system_oauth2_approve" DROP CONSTRAINT "cons134219445_ce1ed48e_B2DF1EF4_F1CEF92F_40F9D5D4";

ALTER TABLE "middle_edu"."system_oauth2_client" RENAME CONSTRAINT "system_oauth2_client_pkey" TO "system_oauth2_client_pkey_78A22747";

ALTER TABLE "middle_edu"."system_oauth2_code" DROP CONSTRAINT "cons134219447_668d1f8_9E0E83F0_EA01F6C5_10F51A09";

ALTER TABLE "middle_edu"."system_oauth2_refresh_token" DROP CONSTRAINT "cons134219448_38d44eb9_7FEF3DD3_715EFA0C_96E8DD9B";

ALTER TABLE "middle_edu"."system_operate_log" DROP CONSTRAINT "cons134219972_a954e8cd_B80E6BCD_87D8865A_3836BE52";

ALTER TABLE "middle_edu"."system_post" DROP CONSTRAINT "cons134219668_4a56af28_89BE8920_92A975F2_4917F7D8";

ALTER TABLE "middle_edu"."system_region" DROP CONSTRAINT "cons134219669_e188ad7d_C56B300E_3A8EC891_49771146";

ALTER TABLE "middle_edu"."system_role" DROP CONSTRAINT "cons134219670_633c6b95_AE9FE5AF_755847C6_18C5DBA0";

ALTER TABLE "middle_edu"."system_role_group" DROP CONSTRAINT "system_role_group_pk";

ALTER TABLE "middle_edu"."system_role_group_bind" DROP CONSTRAINT "system_role_group_bind_pk";

ALTER TABLE "middle_edu"."system_role_menu" DROP CONSTRAINT "cons134219452_ee256949_D166EFD1_7AD42299_E2D748D0";

ALTER TABLE "middle_edu"."system_sensitive_word" DROP CONSTRAINT "cons134219453_52267bd5_2B63ECB7_83CF4918_A174F101";

ALTER TABLE "middle_edu"."system_sms_channel" DROP CONSTRAINT "cons134219454_348aede0_60581B83_6F0A9035_C59EF194";

ALTER TABLE "middle_edu"."system_sms_code" DROP CONSTRAINT "cons134219455_10176bd7_E14D947C_80E1F176_B7C871A7";

ALTER TABLE "middle_edu"."system_sms_log" DROP CONSTRAINT "cons134219456_181c9863_C8AC5621_E9426865_409DA85D";

ALTER TABLE "middle_edu"."system_sms_template" DROP CONSTRAINT "cons134219457_aec6eda6_511786F4_99999689_11D202C4";

ALTER TABLE "middle_edu"."system_social_user" DROP CONSTRAINT "cons134219458_bfca8f64_F83C7039_B1ABF97_5A8795AC";

ALTER TABLE "middle_edu"."system_social_user_bind" DROP CONSTRAINT "cons134219459_da1b0f87_F8ABC1AB_C4D7886_6EB20879";

ALTER TABLE "middle_edu"."system_tenant" DROP CONSTRAINT "cons134219760_ce3e6e6b_B3D14593_176B4D95_206CD438";

ALTER TABLE "middle_edu"."system_tenant" ALTER COLUMN "check_by_period_rule" DROP NOT NULL;

ALTER TABLE "middle_edu"."system_tenant" ALTER COLUMN "check_by_period_rule" DROP DEFAULT;

COMMENT ON COLUMN "middle_edu"."system_tenant"."check_by_period_rule" IS '';

ALTER TABLE "middle_edu"."system_tenant" ALTER COLUMN "enable_attendance_protection" DROP NOT NULL;

ALTER TABLE "middle_edu"."system_tenant" ALTER COLUMN "enable_attendance_protection" DROP DEFAULT;

COMMENT ON COLUMN "middle_edu"."system_tenant"."enable_attendance_protection" IS '';

ALTER TABLE "middle_edu"."system_tenant_approval" DROP CONSTRAINT "cons134219461_69fee787_5F67D4FB_193B4332_F3898832";

ALTER TABLE "middle_edu"."system_tenant_package" DROP CONSTRAINT "cons134219462_2a5289d1_3A97198_7D518CDD_9ECFE666";

ALTER TABLE "middle_edu"."system_tenant_type" DROP CONSTRAINT "cons134219463_a1b9fc50_BE27935E_BA58A583_61EAFD1D";

ALTER TABLE "middle_edu"."system_tenant_type_role" DROP CONSTRAINT "cons134219464_5af91d88_225A8AF9_47CF599E_E5951A27";

ALTER TABLE "middle_edu"."system_timed_task" DROP CONSTRAINT "con_middle_system_timed_task_constraint_1";

ALTER TABLE "middle_edu"."system_timed_task" ALTER COLUMN "message" TYPE text COLLATE "pg_catalog"."default" USING "message"::text;

ALTER TABLE "middle_edu"."system_timed_task" ALTER COLUMN "phone" TYPE text COLLATE "pg_catalog"."default" USING "phone"::text;

ALTER TABLE "middle_edu"."system_timed_task" ALTER COLUMN "title" TYPE text COLLATE "pg_catalog"."default" USING "title"::text;

ALTER TABLE "middle_edu"."system_timed_task" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."system_timed_task" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."system_timed_task" ALTER COLUMN "receiver" TYPE text COLLATE "pg_catalog"."default" USING "receiver"::text;

ALTER TABLE "middle_edu"."system_user_dept" DROP CONSTRAINT "cons134219671_985d9614_F1150FA9_B090AEC3_7B494865_1_2";

ALTER TABLE "middle_edu"."system_user_post" DROP CONSTRAINT "cons134219671_985d9614_F1150FA9_B090AEC3_7B494865";

ALTER TABLE "middle_edu"."system_user_role" DROP CONSTRAINT "cons134219672_f361c86b_3166A8ED_2BE27F56_71A5E2A6";

ALTER TABLE "middle_edu"."system_user_role_619" DROP CONSTRAINT "system_user_role_619_pkey";

ALTER TABLE "middle_edu"."system_user_role_730" DROP CONSTRAINT "system_user_role_copy1_pkey";

ALTER TABLE "middle_edu"."system_user_role_group" DROP CONSTRAINT "system_user_role_group_pk";

ALTER TABLE "middle_edu"."system_user_session" DROP CONSTRAINT "cons134219467_db47c287_DCD24CB7_377BF1E9_2DF6B994";

ALTER TABLE "middle_edu"."system_user_sign" DROP CONSTRAINT "cons134219671_985d9614_F1150FA9_B090AEC3_7B494865_1";

ALTER TABLE "middle_edu"."system_user_sign" ALTER COLUMN "sign" TYPE text COLLATE "pg_catalog"."default" USING "sign"::text;

ALTER TABLE "middle_edu"."system_users" ALTER COLUMN "wxxcx_openid" TYPE text COLLATE "pg_catalog"."default" USING "wxxcx_openid"::text;

ALTER TABLE "middle_edu"."system_users" ALTER COLUMN "wx_unionid" TYPE text COLLATE "pg_catalog"."default" USING "wx_unionid"::text;

ALTER TABLE "middle_edu"."system_users" ALTER COLUMN "employee_id" TYPE text COLLATE "pg_catalog"."default" USING "employee_id"::text;

ALTER TABLE "middle_edu"."system_users" RENAME CONSTRAINT "cons134219680_c0472286_5A3D32D7_8E0C8AC7_91054325" TO "system_users_pk";

ALTER TABLE "middle_edu"."t_class_info" DROP CONSTRAINT "t_class_info_pkey";

ALTER TABLE "middle_edu"."t_class_info" ALTER COLUMN "id" TYPE varchar COLLATE "pg_catalog"."default";

ALTER TABLE "middle_edu"."t_student_basic_info" DROP CONSTRAINT "t_student_basic_info_pkey";

ALTER TABLE "middle_edu"."table_name" DROP CONSTRAINT "con_middle_table_name_constraint_1";

ALTER TABLE "middle_edu"."table_name" ALTER COLUMN "class_committee_name" TYPE text COLLATE "pg_catalog"."default" USING "class_committee_name"::text;

ALTER TABLE "middle_edu"."table_name" ALTER COLUMN "creator" TYPE text COLLATE "pg_catalog"."default" USING "creator"::text;

ALTER TABLE "middle_edu"."table_name" ALTER COLUMN "updater" TYPE text COLLATE "pg_catalog"."default" USING "updater"::text;

ALTER TABLE "middle_edu"."yeartask_endtime" DROP CONSTRAINT "yeartask_endtime_pkey";

ALTER SEQUENCE "middle_edu"."account_api_limit_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_account_account_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_application_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_group_api_group_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_request_record_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_server_group_server_group_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_server_server_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_url_api_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."api_url_param_param_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."dzbp_course_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."dzbp_course_stu_att_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_cadre_information_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_clock_calendar_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_clock_in_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_committee_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_completion_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_completion_template_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_course_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_class_course_order_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_classroom_library_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_clock_in_info_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_completion_template_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_courses_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_elective_release_classes_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_elective_release_courses_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_elective_release_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_elective_trainee_selection_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_graduation_certificate_numbers_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_leave_report_detail_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_leave_report_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_low_permission_id_seq"
    OWNED BY "middle_edu"."edu_low_permission"."id";

ALTER SEQUENCE "middle_edu"."edu_notice_announcement_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_notice_file_url_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_notification_message_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_notification_message_unit_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_plan_config_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_plan_detail_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_plan_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_plan_template_config_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_plan_template_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_rollcall_common_locations_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_rollcall_record_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_rollcall_sign_in_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_rule_location_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_rule_template_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_school_accommodation_attendance_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_shift_management_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_sign_up_unit_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_teacher_course_information_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_teacher_dept_id_seq1"
    OWNED BY "middle_edu"."edu_teacher_dept"."id";

ALTER SEQUENCE "middle_edu"."edu_teacher_information_id_seq1"
    OWNED BY "middle_edu"."edu_teacher_information"."id";

ALTER SEQUENCE "middle_edu"."edu_todo_items_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_todo_items_id_seq1"
    OWNED BY "middle_edu"."edu_todo_items"."id";

ALTER SEQUENCE "middle_edu"."edu_train_file_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_train_file_id_seq1"
    OWNED BY "middle_edu"."edu_train_file"."id";

ALTER SEQUENCE "middle_edu"."edu_trainee_class_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_trainee_group_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_trainee_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_trainee_user_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."edu_trainee_user_id_seq1"
    OWNED BY "middle_edu"."edu_trainee"."user_id";

ALTER SEQUENCE "middle_edu"."edu_xcxmsg_config_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hki_course_user_id_1_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_personnal_basic_information_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_personnel_transfer_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_accessory_info_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_basic_info_back_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_basic_info_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_learning_exp_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_public_book_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_public_paper_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_public_result_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_relationship_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_research_achievement_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_research_project_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_teaching_award_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruit_work_exp_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruitment_batch_management_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."hr_recruitment_position_management_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."kafka_message_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."loudi_cadre_info_change_id_seq"
    OWNED BY "middle_edu"."loudi_cadre_info_change"."id";

ALTER SEQUENCE "middle_edu"."loudi_cadre_training_record_id_seq"
    OWNED BY "middle_edu"."loudi_cadre_training_record"."id";

ALTER SEQUENCE "middle_edu"."loudi_edu_cadre_information_id_seq"
    OWNED BY "middle_edu"."loudi_edu_cadre_information"."id";

ALTER SEQUENCE "middle_edu"."loudi_edu_xcxmsg_his_id_seq"
    OWNED BY "middle_edu"."loudi_edu_xcxmsg_his"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_evaluation_detail_id_seq"
    OWNED BY "middle_edu"."loudi_pg_evaluation_detail"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_evaluation_response_id_seq"
    OWNED BY "middle_edu"."loudi_pg_evaluation_response"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_options_id_seq"
    OWNED BY "middle_edu"."loudi_pg_options"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_question_category_management_id_seq"
    OWNED BY "middle_edu"."loudi_pg_question_category_management"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_question_logic_id_seq"
    OWNED BY "middle_edu"."loudi_pg_question_logic"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_question_management_id_seq"
    OWNED BY "middle_edu"."loudi_pg_question_management"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_questionnaire_detail_id_seq"
    OWNED BY "middle_edu"."loudi_pg_questionnaire_detail"."id";

ALTER SEQUENCE "middle_edu"."loudi_pg_questionnaire_management_id_seq"
    OWNED BY "middle_edu"."loudi_pg_questionnaire_management"."id";

ALTER SEQUENCE "middle_edu"."loudi_registration_review_id_seq"
    OWNED BY "middle_edu"."loudi_registration_review"."id";

ALTER SEQUENCE "middle_edu"."loudi_registration_review_rule_id_seq"
    OWNED BY "middle_edu"."loudi_registration_review_rule"."id";

ALTER SEQUENCE "middle_edu"."notice_read_record_id _seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."notice_read_record_notice_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."notice_read_record_user_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_draft_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_lecture_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_notice_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_notice_updater_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_receive_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_vacation_duty_1_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_vacation_duty_form_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_vacation_duty_form_leader_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_vacation_duty_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."oa_work_schedule_weekly_work_schedule_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."old_dept_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."old_person_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."personnal_party_member_file_id_seq1"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."personnal_position_id_seq1"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."personnal_study_experience_id_seq1"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."personnal_work_experience_id_seq1"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_dict_data_id_seq"
    MAXVALUE 9223372036854775807;

ALTER SEQUENCE "middle_edu"."system_dict_type_id_seq"
    MAXVALUE 9223372036854775807;

ALTER SEQUENCE "middle_edu"."system_message_authority_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_message_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_message_send_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_message_upstream_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_oauth2_client_id_seq"
    MAXVALUE 9223372036854775807;

ALTER SEQUENCE "middle_edu"."system_trainee_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_user_dept_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_user_role_619_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_user_role_merge_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."system_user_sign_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."table_name_id_seq"
    OWNED BY NONE;

ALTER SEQUENCE "middle_edu"."timed_task_id_seq"
    OWNED BY NONE;
